import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // 阻止所有 ygo 相关请求 - 完全静默处理
  if (pathname.includes('/ygo/') || pathname.includes('card_data.json')) {
    // 返回一个最小的响应，避免日志输出
    return new Response('', {
      status: 200,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Content-Type': 'text/plain',
        'Content-Length': '0'
      }
    })
  }

  // 阻止其他可疑请求模式
  const suspiciousPatterns = [
    '/api/ygo',
    '/assets/ygo',
    '/data/ygo',
    '/json/ygo',
    'yugioh',
    'duel',
    'card_database'
  ]

  for (const pattern of suspiciousPatterns) {
    if (pathname.toLowerCase().includes(pattern.toLowerCase())) {
      return new Response('', {
        status: 200,
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Content-Type': 'text/plain',
          'Content-Length': '0'
        }
      })
    }
  }

  // 正常请求继续处理
  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!_next/static|_next/image|favicon.ico).*)',
  ],
}
