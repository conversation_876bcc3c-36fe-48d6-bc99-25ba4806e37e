import { CertificateTemplate, CertificateCategory } from '@/types/certificate';

/**
 * 证书模板配置
 * 基于文档中的精确规格定义
 */

export const CERTIFICATE_TEMPLATES: CertificateTemplate[] = [
  // Achievement Category Template 1
  {
    id: 'achievement-template-1',
    name: 'achievement-template-1',
    displayName: 'Professional Achievement Certificate',
    description: 'Professional blue-themed certificate with elegant borders',

    // Category information
    category: CertificateCategory.ACHIEVEMENT,
    tags: ['professional', 'business', 'blue', 'elegant', 'formal'],

    // Availability status
    status: 'open' as const,

    // 视觉属性
    preview: '/templates/achievement1.png',
    backgroundImage: '/templates/achievement1-blank.png',
    orientation: 'landscape' as const,
    aspectRatio: 4/3,

    // SEO属性
    seoTitle: 'Classic Business Achievement Certificate Template | Professional Blue Design',
    seoDescription: 'Create professional achievement certificates with our classic business template. Features elegant blue design with customizable text fields. Download as high-quality PDF.',
    seoKeywords: ['classic business certificate template', 'professional blue certificate', 'business achievement award', 'corporate recognition certificate'],

    // 样式配置
    style: {
      background: '#FFFFFF',
      border: '3pt solid #1E40AF',
      colors: {
        primary: '#1E40AF',
        secondary: '#3B82F6',
        background: '#FFFFFF',
        text: '#1F2937',
        border: '#1E40AF',
      },
      fonts: {
        name: {
          family: 'EB Garamond',
          size: 28,
          weight: 600,
          color: '#1F2937',
        },
        body: {
          family: 'Inter',
          size: 14,
          weight: 400,
          color: '#374151',
        },
        signature: {
          family: 'Dancing Script',
          size: 24,
          weight: 400,
          color: '#1F2937',
        },
      },
    },
    layout: {
      name: {
        x: 109,
        y: 98,
        width: 400,
        height: 40,
        align: 'center',
        fontSize: 180,
        fontFamily: 'EB Garamond',
        color: '#d2af8c',
        fontWeight: 600,
      },
      details: {
        x: 33,
        y: 119,
        width: 225,
        height: 24,
        align: 'center',
        fontSize: 14,
        fontFamily: 'EB Garamond',
        color: '#374151',
        fontWeight: 400,
      },
      date: {
        x: 150,
        y: 250,
        width: 150,
        height: 30,
        align: 'left',
        fontSize: 14,
        fontFamily: 'EB Garamond',
        color: '#374151',
        fontWeight: 400,
      },
      signature: {
        x: 445,
        y: 250,
        width: 150,
        height: 30,
        align: 'right',
        fontSize: 24,
        fontFamily: 'EB Garamond',
        color: '#1F2937',
        fontWeight: 400,
      },
    },
    constraints: {
      nameMaxLength: 50,
      nameMinLength: 1,
      dateMaxLength: 20,
      dateMinLength: 1,
      signatureMaxLength: 30,
      signatureMinLength: 1,
      detailsMaxLength: 200,
      detailsMinLength: 10,
    },
    validation: {
      namePattern: /^[a-zA-Z\s\-\.\']+$/,
      datePattern: /^[a-zA-Z0-9\s\,\-\.]+$/,
      signaturePattern: /^[a-zA-Z\s\-\.\']+$/,
      detailsPattern: /^[a-zA-Z0-9\s\,\.\!\?\-\'\"\(\)]+$/,
    },
  },

  // Achievement Category Template 2
  {
    id: 'achievement-template-2',
    name: 'achievement-template-2',
    displayName: 'Distinguished Achievement Certificate',
    description: 'Distinguished certificate design for outstanding achievements',

    // 分类信息
    category: CertificateCategory.ACHIEVEMENT,
    tags: ['distinguished', 'achievement', 'premium', 'elegant', 'formal'],
    status: 'open' as const,

    // 视觉属性
    preview: '/templates/achievement2.png',
    backgroundImage: '/templates/achievement2-blank.png',
    orientation: 'landscape' as const,
    aspectRatio: 4/3,

    // SEO属性
    seoTitle: 'Distinguished Achievement Certificate Template | Premium Professional Design',
    seoDescription: 'Create distinguished achievement certificates with our premium template. Perfect for outstanding performance recognition and professional awards.',
    seoKeywords: ['distinguished achievement certificate', 'premium certificate design', 'professional achievement award', 'outstanding performance certificate'],
    style: {
      background: '#FFFFFF',
      border: '1pt solid #6B7280',
      colors: {
        primary: '#000000',
        secondary: '#6B7280',
        background: '#FFFFFF',
        text: '#374151',
        border: '#6B7280',
      },
      fonts: {
        name: {
          family: 'Inter',
          size: 26,
          weight: 600,
          color: '#000000',
        },
        body: {
          family: 'Inter',
          size: 14,
          weight: 400,
          color: '#374151',
        },
        signature: {
          family: 'Inter',
          size: 16,
          weight: 500,
          color: '#6B7280',
        },
      },
    },
    layout: {
      name: {
        x: 297.64,
        y: 320,
        width: 400,
        height: 35,
        align: 'center',
        fontSize: 26,
        fontFamily: 'Inter',
        color: '#000000',
        fontWeight: 600,
      },
      details: {
        x: 297.64,
        y: 420,
        width: 450,
        height: 80,
        align: 'center',
        fontSize: 14,
        fontFamily: 'Inter',
        color: '#374151',
        fontWeight: 400,
      },
      date: {
        x: 150,
        y: 600,
        width: 150,
        height: 25,
        align: 'left',
        fontSize: 14,
        fontFamily: 'Inter',
        color: '#374151',
        fontWeight: 400,
      },
      signature: {
        x: 445,
        y: 600,
        width: 150,
        height: 25,
        align: 'right',
        fontSize: 16,
        fontFamily: 'Inter',
        color: '#6B7280',
        fontWeight: 500,
      },
    },
    constraints: {
      nameMaxLength: 50,
      nameMinLength: 1,
      dateMaxLength: 20,
      dateMinLength: 1,
      signatureMaxLength: 30,
      signatureMinLength: 1,
      detailsMaxLength: 200,
      detailsMinLength: 10,
    },
    validation: {
      namePattern: /^[a-zA-Z\s\-\.\']+$/,
      datePattern: /^[a-zA-Z0-9\s\,\-\.]+$/,
      signaturePattern: /^[a-zA-Z\s\-\.\']+$/,
      detailsPattern: /^[a-zA-Z0-9\s\,\.\!\?\-\'\"\(\)]+$/,
    },
  },

  // Achievement Category Template 3 (Copy of Template 1 with different preview)
  {
    id: 'achievement-template-3',
    name: 'achievement-template-3',
    displayName: 'Golden Achievement Certificate',
    description: 'Professional golden-themed certificate with elegant borders',

    // 分类信息
    category: CertificateCategory.ACHIEVEMENT,
    tags: ['professional', 'business', 'golden', 'elegant', 'formal'],
    status: 'open' as const,

    // 视觉属性
    preview: '/templates/achievement3.png',
    backgroundImage: '/templates/achievement3-blank.png',
    orientation: 'landscape' as const,
    aspectRatio: 4/3,

    // SEO属性
    seoTitle: 'Golden Achievement Certificate Template | Professional Design',
    seoDescription: 'Create professional achievement certificates with our golden template. Features elegant design with customizable text fields. Download as high-quality PDF.',
    seoKeywords: ['golden achievement certificate template', 'professional certificate', 'business achievement award', 'corporate recognition certificate'],

    // 样式配置
    style: {
      background: '#FFFFFF',
      border: '3pt solid #D97706',
      colors: {
        primary: '#D97706',
        secondary: '#F59E0B',
        background: '#FFFFFF',
        text: '#1F2937',
        border: '#D97706',
      },
      fonts: {
        name: {
          family: 'Playfair Display',
          size: 32,
          weight: 600,
          color: '#1F2937',
        },
        body: {
          family: 'Inter',
          size: 14,
          weight: 400,
          color: '#374151',
        },
        signature: {
          family: 'Dancing Script',
          size: 24,
          weight: 400,
          color: '#1F2937',
        },
      },
    },

    // 布局配置
    layout: {
      name: {
        x: 400,
        y: 300,
        width: 400,
        height: 50,
        align: 'center',
        fontSize: 32,
        fontFamily: 'Playfair Display',
        color: '#1F2937',
        fontWeight: 600,
      },
      details: {
        x: 400,
        y: 350,
        width: 500,
        height: 80,
        align: 'center',
        fontSize: 16,
        fontFamily: 'Inter',
        color: '#374151',
        fontWeight: 400,
      },
      date: {
        x: 150,
        y: 650,
        width: 150,
        height: 30,
        align: 'left',
        fontSize: 14,
        fontFamily: 'Inter',
        color: '#374151',
        fontWeight: 400,
      },
      signature: {
        x: 445,
        y: 650,
        width: 150,
        height: 30,
        align: 'right',
        fontSize: 24,
        fontFamily: 'Dancing Script',
        color: '#1F2937',
        fontWeight: 400,
      },
    },
    constraints: {
      nameMaxLength: 50,
      nameMinLength: 1,
      dateMaxLength: 20,
      dateMinLength: 1,
      signatureMaxLength: 30,
      signatureMinLength: 1,
      detailsMaxLength: 200,
      detailsMinLength: 10,
    },
    validation: {
      namePattern: /^[a-zA-Z\s\-\.\']+$/,
      datePattern: /^[a-zA-Z0-9\s\,\-\.]+$/,
      signaturePattern: /^[a-zA-Z\s\-\.\']+$/,
      detailsPattern: /^[a-zA-Z0-9\s\,\.\!\?\-\'\"\(\)]+$/,
    },
  },

  // Achievement Category Template 4 (Copy of Template 2 with different preview)
  {
    id: 'achievement-template-4',
    name: 'achievement-template-4',
    displayName: 'Classic Achievement Certificate',
    description: 'Clean and classic certificate design with minimalist aesthetics',

    // 分类信息
    category: CertificateCategory.ACHIEVEMENT,
    tags: ['classic', 'minimalist', 'clean', 'contemporary', 'simple'],
    status: 'open' as const,

    // 视觉属性
    preview: '/templates/achievement4.png',
    backgroundImage: '/templates/achievement4-blank.png',
    orientation: 'landscape' as const,
    aspectRatio: 4/3,

    // SEO属性
    seoTitle: 'Classic Achievement Certificate Template | Minimalist Design',
    seoDescription: 'Create clean achievement certificates with our classic template. Features minimalist design with professional typography. Perfect for modern businesses.',
    seoKeywords: ['classic achievement certificate template', 'minimalist certificate', 'clean certificate design', 'modern business certificate'],

    // 样式配置
    style: {
      background: '#FFFFFF',
      border: '2pt solid #6B7280',
      colors: {
        primary: '#374151',
        secondary: '#6B7280',
        background: '#FFFFFF',
        text: '#1F2937',
        border: '#6B7280',
      },
      fonts: {
        name: {
          family: 'Inter',
          size: 28,
          weight: 600,
          color: '#1F2937',
        },
        body: {
          family: 'Inter',
          size: 14,
          weight: 400,
          color: '#6B7280',
        },
        signature: {
          family: 'Inter',
          size: 18,
          weight: 500,
          color: '#374151',
        },
      },
    },

    // 布局配置
    layout: {
      name: {
        x: 400,
        y: 300,
        width: 400,
        height: 50,
        align: 'center',
        fontSize: 28,
        fontFamily: 'Inter',
        color: '#1F2937',
        fontWeight: 600,
      },
      details: {
        x: 400,
        y: 350,
        width: 500,
        height: 80,
        align: 'center',
        fontSize: 16,
        fontFamily: 'Inter',
        color: '#6B7280',
        fontWeight: 400,
      },
      date: {
        x: 150,
        y: 650,
        width: 150,
        height: 30,
        align: 'left',
        fontSize: 14,
        fontFamily: 'Inter',
        color: '#6B7280',
        fontWeight: 400,
      },
      signature: {
        x: 445,
        y: 650,
        width: 150,
        height: 30,
        align: 'right',
        fontSize: 18,
        fontFamily: 'Inter',
        color: '#374151',
        fontWeight: 500,
      },
    },
    constraints: {
      nameMaxLength: 50,
      nameMinLength: 1,
      dateMaxLength: 20,
      dateMinLength: 1,
      signatureMaxLength: 30,
      signatureMinLength: 1,
      detailsMaxLength: 200,
      detailsMinLength: 10,
    },
    validation: {
      namePattern: /^[a-zA-Z\s\-\.\']+$/,
      datePattern: /^[a-zA-Z0-9\s\,\-\.]+$/,
      signaturePattern: /^[a-zA-Z\s\-\.\']+$/,
      detailsPattern: /^[a-zA-Z0-9\s\,\.\!\?\-\'\"\(\)]+$/,
    },
  },

  // Achievement Category Template 5 (Copy of Template 1 with different preview)
  {
    id: 'achievement-template-5',
    name: 'achievement-template-5',
    displayName: 'Creative Achievement Certificate',
    description: 'Creative purple-themed certificate with modern design',

    // 分类信息
    category: CertificateCategory.ACHIEVEMENT,
    tags: ['creative', 'modern', 'purple', 'artistic', 'contemporary'],
    status: 'open' as const,

    // 视觉属性
    preview: '/templates/achievement5.png',
    backgroundImage: '/templates/achievement5-blank.png',
    orientation: 'landscape' as const,
    aspectRatio: 4/3,

    // SEO属性
    seoTitle: 'Creative Achievement Certificate Template | Modern Design',
    seoDescription: 'Create modern achievement certificates with our creative template. Features artistic design with vibrant colors. Perfect for creative industries.',
    seoKeywords: ['creative achievement certificate template', 'modern certificate', 'artistic certificate design', 'purple certificate'],

    // 样式配置
    style: {
      background: '#FFFFFF',
      border: '3pt solid #7C3AED',
      colors: {
        primary: '#7C3AED',
        secondary: '#A855F7',
        background: '#FFFFFF',
        text: '#1F2937',
        border: '#7C3AED',
      },
      fonts: {
        name: {
          family: 'Playfair Display',
          size: 32,
          weight: 600,
          color: '#1F2937',
        },
        body: {
          family: 'Inter',
          size: 14,
          weight: 400,
          color: '#374151',
        },
        signature: {
          family: 'Dancing Script',
          size: 24,
          weight: 400,
          color: '#1F2937',
        },
      },
    },

    // 布局配置
    layout: {
      name: {
        x: 400,
        y: 300,
        width: 400,
        height: 50,
        align: 'center',
        fontSize: 32,
        fontFamily: 'Playfair Display',
        color: '#1F2937',
        fontWeight: 600,
      },
      details: {
        x: 400,
        y: 350,
        width: 500,
        height: 80,
        align: 'center',
        fontSize: 16,
        fontFamily: 'Inter',
        color: '#374151',
        fontWeight: 400,
      },
      date: {
        x: 150,
        y: 650,
        width: 150,
        height: 30,
        align: 'left',
        fontSize: 14,
        fontFamily: 'Inter',
        color: '#374151',
        fontWeight: 400,
      },
      signature: {
        x: 445,
        y: 650,
        width: 150,
        height: 30,
        align: 'right',
        fontSize: 24,
        fontFamily: 'Dancing Script',
        color: '#1F2937',
        fontWeight: 400,
      },
    },
    constraints: {
      nameMaxLength: 50,
      nameMinLength: 1,
      dateMaxLength: 20,
      dateMinLength: 1,
      signatureMaxLength: 30,
      signatureMinLength: 1,
      detailsMaxLength: 200,
      detailsMinLength: 10,
    },
    validation: {
      namePattern: /^[a-zA-Z\s\-\.\']+$/,
      datePattern: /^[a-zA-Z0-9\s\,\-\.]+$/,
      signaturePattern: /^[a-zA-Z\s\-\.\']+$/,
      detailsPattern: /^[a-zA-Z0-9\s\,\.\!\?\-\'\"\(\)]+$/,
    },
  },
{
    id: 'achievement-template-6',
    name: 'achievement-template-6',
    displayName: 'Professional Achievement Certificate',
    description: 'Professional blue-themed certificate with elegant borders',

    // 分类信息
    category: CertificateCategory.ACHIEVEMENT,
    tags: ['professional', 'business', 'blue', 'elegant', 'formal'],
    status: 'open' as const,

    // 视觉属性
    preview: '/templates/achievement6.png',
    backgroundImage: '/templates/achievement6-blank.png',
    orientation: 'landscape' as const,
    aspectRatio: 4/3,

    // SEO属性
    seoTitle: 'Classic Business Achievement Certificate Template | Professional Blue Design',
    seoDescription: 'Create professional achievement certificates with our classic business template. Features elegant blue design with customizable text fields. Download as high-quality PDF.',
    seoKeywords: ['classic business certificate template', 'professional blue certificate', 'business achievement award', 'corporate recognition certificate'],

    // 样式配置
    style: {
      background: '#FFFFFF',
      border: '3pt solid #1E40AF',
      colors: {
        primary: '#1E40AF',
        secondary: '#3B82F6',
        background: '#FFFFFF',
        text: '#1F2937',
        border: '#1E40AF',
      },
      fonts: {
        
        name: {
          family: 'Playfair Display',
          size: 28,
          weight: 600,
          color: '#1F2937',
        },
        body: {
          family: 'Inter',
          size: 14,
          weight: 400,
          color: '#374151',
        },
        signature: {
          family: 'Dancing Script',
          size: 24,
          weight: 400,
          color: '#1F2937',
        },
      },
    },
    layout: {
      name: {
        x: 297.64,
        y: 300,
        width: 400,
        height: 40,
        align: 'center',
        fontSize: 28,
        fontFamily: 'Playfair Display',
        color: '#1F2937',
        fontWeight: 600,
      },
      details: {
        x: 297.64,
        y: 400,
        width: 450,
        height: 100,
        align: 'center',
        fontSize: 14,
        fontFamily: 'Inter',
        color: '#374151',
        fontWeight: 400,
      },
      date: {
        x: 150,
        y: 650,
        width: 150,
        height: 30,
        align: 'left',
        fontSize: 14,
        fontFamily: 'Inter',
        color: '#374151',
        fontWeight: 400,
      },
      signature: {
        x: 445,
        y: 650,
        width: 150,
        height: 30,
        align: 'right',
        fontSize: 24,
        fontFamily: 'Dancing Script',
        color: '#1F2937',
        fontWeight: 400,
      },
    },
    constraints: {
      nameMaxLength: 50,
      nameMinLength: 1,
      dateMaxLength: 20,
      dateMinLength: 1,
      signatureMaxLength: 30,
      signatureMinLength: 1,
      detailsMaxLength: 200,
      detailsMinLength: 10,
    },
    validation: {
      namePattern: /^[a-zA-Z\s\-\.\']+$/,
      datePattern: /^[a-zA-Z0-9\s\,\-\.]+$/,
      signaturePattern: /^[a-zA-Z\s\-\.\']+$/,
      detailsPattern: /^[a-zA-Z0-9\s\,\.\!\?\-\'\"\(\)]+$/,
    },
  },
  // Completion Category Template 1
  {
    id: 'completion-template-1',
    name: 'completion-template-1',
    displayName: 'Professional Completion Certificate',
    description: 'Clean and professional design for course completion',

    // 分类信息
    category: CertificateCategory.COMPLETION,
    tags: ['professional', 'completion', 'clean', 'modern', 'educational'],
    status: 'open' as const,

    // 视觉属性
    preview: '/templates/completion1.png',
    backgroundImage: '/templates/completion1-blank.png',
    orientation: 'portrait' as const,
    aspectRatio: 3/4,

    // SEO属性
    seoTitle: 'Professional Completion Certificate Template | Course & Training Certificates',
    seoDescription: 'Create professional completion certificates for courses and training programs. Clean design perfect for educational achievements.',
    seoKeywords: ['professional completion certificate', 'course completion template', 'training certificate', 'educational certificate'],
    style: {
      background: '#F0FDF4',
      border: '2pt solid #059669',
      colors: {
        primary: '#059669',
        secondary: '#10B981',
        background: '#F0FDF4',
        text: '#374151',
        border: '#059669',
      },
      fonts: {
        name: {
          family: 'Dancing Script',
          size: 26,
          weight: 400,
          color: '#1F2937',
        },
        body: {
          family: 'Dancing Script',
          size: 14,
          weight: 400,
          color: '#374151',
        },
        signature: {
          family: 'Dancing Script',
          size: 22,
          weight: 400,
          color: '#059669',
        },
      },
    },
    layout: {
      name: {
        x: 297.5,
        y: 450,
        width: 400,
        height: 40,
        align: 'center',
        fontSize: 38,
        fontFamily: 'Dancing Script',
        color: '#1F2937',
        fontWeight: 400,
      },
      details: {
        x: 297.5,
        y: 550,
        width: 450,
        height: 120,
        align: 'center',
        fontSize: 20,
        fontFamily: 'Dancing Script',
        color: '#374151',
        fontWeight: 400,
      },
      date: {
        x: 100,
        y: 700,
        width: 150,
        height: 30,
        align: 'left',
        fontSize: 24,
        fontFamily: 'Dancing Script',
        color: '#374151',
        fontWeight: 400,
      },
      signature: {
        x: 495,
        y: 700,
        width: 150,
        height: 30,
        align: 'right',
        fontSize: 24,
        fontFamily: 'Dancing Script',
        color: '#374151',
        fontWeight: 400,
      },
    },
    constraints: {
      nameMaxLength: 50,
      nameMinLength: 1,
      dateMaxLength: 20,
      dateMinLength: 1,
      signatureMaxLength: 30,
      signatureMinLength: 1,
      detailsMaxLength: 200,
      detailsMinLength: 10,
    },
    validation: {
      namePattern: /^[a-zA-Z\s\-\.\']+$/,
      datePattern: /^[a-zA-Z0-9\s\,\-\.]+$/,
      signaturePattern: /^[a-zA-Z\s\-\.\']+$/,
      detailsPattern: /^[a-zA-Z0-9\s\,\.\!\?\-\'\"\(\)]+$/,
    },
  },

  // Completion Category Template 2
  {
    id: 'completion-template-2',
    name: 'completion-template-2',
    displayName: 'Professional Completion Certificate',
    description: 'Clean and professional design for course completion',

    // 分类信息
    category: CertificateCategory.COMPLETION,
    tags: ['professional', 'completion', 'clean', 'modern', 'educational'],
    status: 'open' as const,

    // 视觉属性
    preview: '/templates/completion2.png',
    backgroundImage: '/templates/completion2-blank.png',
    orientation: 'portrait' as const,
    aspectRatio: 3/4,

    // SEO属性
    seoTitle: 'Professional Completion Certificate Template | Course & Training Certificates',
    seoDescription: 'Create professional completion certificates for courses and training programs. Clean design perfect for educational achievements.',
    seoKeywords: ['professional completion certificate', 'course completion template', 'training certificate', 'educational certificate'],
    style: {
      background: '#F0FDF4',
      border: '2pt solid #059669',
      colors: {
        primary: '#059669',
        secondary: '#10B981',
        background: '#F0FDF4',
        text: '#374151',
        border: '#059669',
      },
      fonts: {
        
        name: {
          family: 'Dancing Script',
          size: 26,
          weight: 600,
          color: '#1F2937',
        },
        body: {
          family: 'Dancing Script',
          size: 14,
          weight: 400,
          color: '#374151',
        },
        signature: {
          family: 'Dancing Script',
          size: 22,
          weight: 400,
          color: '#374151',
        },
      },
    },
    layout: {
      
      name: {
        x: 297.64,
        y: 460,
        width: 400,
        height: 35,
        align: 'center',
        fontSize: 38,
        fontFamily: 'Dancing Script',
        color: '#1F2937',
        fontWeight: 600,
      },
      details: {
        x: 297.64,
        y: 560,
        width: 450,
        height: 90,
        align: 'center',
        fontSize: 20,
        fontFamily: 'Dancing Script',
        color: '#374151',
        fontWeight: 400,
      },
      date: {
        x: 130,
        y: 700,
        width: 150,
        height: 28,
        align: 'left',
        fontSize: 22,
        fontFamily: 'Dancing Script',
        color: '#374151',
        fontWeight: 400,
      },
      signature: {
        x: 465,
        y: 700,
        width: 150,
        height: 28,
        align: 'right',
        fontSize: 22,
        fontFamily: 'Dancing Script',
        color: '#374151',
        fontWeight: 400,
      },
    },
    constraints: {
      nameMaxLength: 50,
      nameMinLength: 1,
      dateMaxLength: 20,
      dateMinLength: 1,
      signatureMaxLength: 30,
      signatureMinLength: 1,
      detailsMaxLength: 200,
      detailsMinLength: 10,
    },
    validation: {
      namePattern: /^[a-zA-Z\s\-\.\']+$/,
      datePattern: /^[a-zA-Z0-9\s\,\-\.]+$/,
      signaturePattern: /^[a-zA-Z\s\-\.\']+$/,
      detailsPattern: /^[a-zA-Z0-9\s\,\.\!\?\-\'\"\(\)]+$/,
    },
  },
  
  
  // Completion Category Template 3
  {
    id: 'completion-template-3',
    name: 'completion-template-3',
    displayName: 'Professional Completion Certificate',
    description: 'Clean and professional design for course completion',

    // 分类信息
    category: CertificateCategory.COMPLETION,
    tags: ['professional', 'completion', 'clean', 'modern', 'educational'],
    status: 'open' as const,

    // 视觉属性
    preview: '/templates/completion3.png',
    backgroundImage: '/templates/completion3-blank.png',
    orientation: 'portrait' as const,
    aspectRatio: 3/4,

    // SEO属性
    seoTitle: 'Professional Completion Certificate Template | Course & Training Certificates',
    seoDescription: 'Create professional completion certificates for courses and training programs. Clean design perfect for educational achievements.',
    seoKeywords: ['professional completion certificate', 'course completion template', 'training certificate', 'educational certificate'],
    style: {
      background: '#F0FDF4',
      border: '2pt solid #059669',
      colors: {
        primary: '#059669',
        secondary: '#10B981',
        background: '#F0FDF4',
        text: '#374151',
        border: '#059669',
      },
      fonts: {
        name: {
          family: 'Dancing Script',
          size: 38,
          weight: 600,
          color: '#1F2937',
        },
        body: {
          family: 'Dancing Script',
          size: 20,
          weight: 400,
          color: '#374151',
        },
        signature: {
          family: 'Dancing Script',
          size: 22,
          weight: 400,
          color: '#374151',
        },
      },
    },
    layout: {
      
      name: {
        x: 297.64,
        y: 530,
        width: 400,
        height: 35,
        align: 'center',
        fontSize: 38,
        fontFamily: 'Dancing Script',
        color: '#1F2937',
        fontWeight: 600,
      },
      details: {
        x: 297.64,
        y: 630,
        width: 450,
        height: 90,
        align: 'center',
        fontSize: 20,
        fontFamily: 'Dancing Script',
        color: '#374151',
        fontWeight: 400,
      },
      date: {
        x: 130,
        y: 690,
        width: 150,
        height: 28,
        align: 'left',
        fontSize: 22,
        fontFamily: 'Dancing Script',
        color: '#374151',
        fontWeight: 400,
      },
      signature: {
        x: 465,
        y: 690,
        width: 150,
        height: 28,
        align: 'right',
        fontSize: 22,
        fontFamily: 'Dancing Script',
        color: '#374151',
        fontWeight: 400,
      },
    },
    constraints: {
      nameMaxLength: 50,
      nameMinLength: 1,
      dateMaxLength: 20,
      dateMinLength: 1,
      signatureMaxLength: 30,
      signatureMinLength: 1,
      detailsMaxLength: 200,
      detailsMinLength: 10,
    },
    validation: {
      namePattern: /^[a-zA-Z\s\-\.\']+$/,
      datePattern: /^[a-zA-Z0-9\s\,\-\.]+$/,
      signaturePattern: /^[a-zA-Z\s\-\.\']+$/,
      detailsPattern: /^[a-zA-Z0-9\s\,\.\!\?\-\'\"\(\)]+$/,
    },
  },


  // Participation Category Template 1
  {
    id: 'participation-template-1',
    name: 'participation-template-1',
    displayName: 'Event Participation Certificate',
    description: 'Professional certificate for event and workshop participation',

    // 分类信息
    category: CertificateCategory.PARTICIPATION,
    tags: ['participation', 'event', 'workshop', 'professional', 'modern'],
    status: 'open' as const,

    // 视觉属性
    preview: '/templates/participation-1.png',
    backgroundImage: '/templates/participation-1.png',
    orientation: 'portrait' as const,
    aspectRatio: 3/4,

    // SEO属性
    seoTitle: 'Event Participation Certificate Template | Workshop & Conference Certificates',
    seoDescription: 'Create professional participation certificates for events, workshops, and conferences. Perfect for acknowledging attendance and engagement.',
    seoKeywords: ['event participation certificate', 'workshop certificate template', 'conference participation', 'professional event certificate'],
    style: {
      background: '#FFFFFF',
      border: '2pt solid #333333',
      colors: {
        primary: '#333333',
        secondary: '#666666',
        background: '#FFFFFF',
        text: '#000000',
        border: '#333333',
      },
      fonts: {
        
        name: {
          family: 'Arial',
          size: 24,
          weight: 600,
          color: '#000000',
        },
        body: {
          family: 'Arial',
          size: 14,
          weight: 400,
          color: '#333333',
        },
        signature: {
          family: 'Arial',
          size: 20,
          weight: 400,
          color: '#000000',
        },
      },
    },
    layout: {
      
      name: {
        x: 300,
        y: 350,
        width: 400,
        height: 40,
        align: 'center' as const,
        fontSize: 24,
        fontFamily: 'Arial',
        color: '#000000',
        fontWeight: 600,
      },
      date: {
        x: 100,
        y: 800,
        width: 200,
        height: 30,
        align: 'left' as const,
        fontSize: 14,
        fontFamily: 'Arial',
        color: '#333333',
      },
      signature: {
        x: 500,
        y: 800,
        width: 200,
        height: 30,
        align: 'right' as const,
        fontSize: 20,
        fontFamily: 'Arial',
        color: '#000000',
      },
      details: {
        x: 300,
        y: 450,
        width: 400,
        height: 100,
        align: 'center' as const,
        fontSize: 14,
        fontFamily: 'Arial',
        color: '#333333',
      },
    },
    constraints: {
      nameMaxLength: 50,
      nameMinLength: 1,
      dateMaxLength: 20,
      dateMinLength: 1,
      signatureMaxLength: 30,
      signatureMinLength: 1,
      detailsMaxLength: 200,
      detailsMinLength: 10,
    },
    validation: {
      namePattern: /^[a-zA-Z\s\-\.\']+$/,
      datePattern: /^[a-zA-Z0-9\s\,\-\.]+$/,
      signaturePattern: /^[a-zA-Z\s\-\.\']+$/,
      detailsPattern: /^[a-zA-Z0-9\s\,\.\!\?\-\'\"\(\)]+$/,
    },
  },

  // Participation Category Template 2
  {
    id: 'participation-template-2',
    name: 'participation-template-2',
    displayName: 'Workshop Participation Certificate',
    description: 'Elegant certificate for workshop and seminar participation',

    // 分类信息
    category: CertificateCategory.PARTICIPATION,
    tags: ['participation', 'workshop', 'seminar', 'elegant', 'professional'],
    status: 'open' as const,

    // 视觉属性
    preview: '/templates/participation-2.png',
    backgroundImage: '/templates/participation-2.png',
    orientation: 'portrait' as const,
    aspectRatio: 3/4,

    // SEO属性
    seoTitle: 'Workshop Participation Certificate Template | Seminar & Training Certificates',
    seoDescription: 'Create elegant participation certificates for workshops and seminars. Perfect for acknowledging learning engagement and professional development.',
    seoKeywords: ['workshop participation certificate', 'seminar certificate template', 'training participation', 'professional development certificate'],
    style: {
      background: '#FFFFFF',
      border: '2pt solid #333333',
      colors: {
        primary: '#333333',
        secondary: '#666666',
        background: '#FFFFFF',
        text: '#000000',
        border: '#333333',
      },
      fonts: {
        
        name: {
          family: 'Arial',
          size: 24,
          weight: 600,
          color: '#000000',
        },
        body: {
          family: 'Arial',
          size: 14,
          weight: 400,
          color: '#333333',
        },
        signature: {
          family: 'Arial',
          size: 20,
          weight: 400,
          color: '#000000',
        },
      },
    },
    layout: {
      
      name: {
        x: 300,
        y: 350,
        width: 400,
        height: 40,
        align: 'center' as const,
        fontSize: 24,
        fontFamily: 'Arial',
        color: '#000000',
        fontWeight: 600,
      },
      date: {
        x: 100,
        y: 800,
        width: 200,
        height: 30,
        align: 'left' as const,
        fontSize: 14,
        fontFamily: 'Arial',
        color: '#333333',
      },
      signature: {
        x: 500,
        y: 800,
        width: 200,
        height: 30,
        align: 'right' as const,
        fontSize: 20,
        fontFamily: 'Arial',
        color: '#000000',
      },
      details: {
        x: 300,
        y: 450,
        width: 400,
        height: 100,
        align: 'center' as const,
        fontSize: 14,
        fontFamily: 'Arial',
        color: '#333333',
      },
    },
    constraints: {
      nameMaxLength: 50,
      nameMinLength: 1,
      dateMaxLength: 20,
      dateMinLength: 1,
      signatureMaxLength: 30,
      signatureMinLength: 1,
      detailsMaxLength: 200,
      detailsMinLength: 10,
    },
    validation: {
      namePattern: /^[a-zA-Z\s\-\.\']+$/,
      datePattern: /^[a-zA-Z0-9\s\,\-\.]+$/,
      signaturePattern: /^[a-zA-Z\s\-\.\']+$/,
      detailsPattern: /^[a-zA-Z0-9\s\,\.\!\?\-\'\"\(\)]+$/,
    },
  },

  // Participation Category Template 3
  {
    id: 'participation-template-3',
    name: 'participation-template-3',
    displayName: 'Conference Participation Certificate',
    description: 'Professional certificate for conference and summit participation',
    category: CertificateCategory.PARTICIPATION,
    tags: ['participation', 'conference', 'summit', 'professional', 'networking'],
    status: 'open' as const,
    preview: '/templates/participation-3.png',
    backgroundImage: '/templates/participation-3.png',
    orientation: 'portrait' as const,
    aspectRatio: 3/4,
    seoTitle: 'Conference Participation Certificate Template',
    seoDescription: 'Create professional participation certificates for conferences and summits.',
    seoKeywords: ['conference participation certificate', 'summit certificate template'],
    style: {
      background: '#FFFFFF',
      border: '3pt solid #DC2626',
      colors: { primary: '#DC2626', secondary: '#EF4444', background: '#FFFFFF', text: '#1F2937', border: '#DC2626' },
      fonts: {
        name: { family: 'Playfair Display', size: 32, weight: 600, color: '#1F2937' },
        body: { family: 'Inter', size: 14, weight: 400, color: '#374151' },
        signature: { family: 'Dancing Script', size: 24, weight: 400, color: '#1F2937' }
      }
    },
    layout: {
      name: { x: 300, y: 350, width: 400, height: 50, align: 'center', fontSize: 32, fontFamily: 'Playfair Display', color: '#1F2937', fontWeight: 600 },
      details: { x: 300, y: 450, width: 400, height: 100, align: 'center', fontSize: 16, fontFamily: 'Inter', color: '#374151', fontWeight: 400 },
      date: { x: 100, y: 800, width: 150, height: 30, align: 'left', fontSize: 14, fontFamily: 'Inter', color: '#374151', fontWeight: 400 },
      signature: { x: 500, y: 800, width: 150, height: 30, align: 'right', fontSize: 24, fontFamily: 'Dancing Script', color: '#1F2937', fontWeight: 400 }
    },
    constraints: { nameMaxLength: 50, nameMinLength: 1, dateMaxLength: 20, dateMinLength: 1, signatureMaxLength: 30, signatureMinLength: 1, detailsMaxLength: 200, detailsMinLength: 10 },
    validation: { namePattern: /^[a-zA-Z\s\-.\']+$/, datePattern: /^[a-zA-Z0-9\s,\-.]+$/, signaturePattern: /^[a-zA-Z\s\-.\']+$/, detailsPattern: /^[a-zA-Z0-9\s,.!?\-\'"()]+$/ }
  },

  // Participation Category Template 4
  {
    id: 'participation-template-4',
    name: 'participation-template-4',
    displayName: 'Training Participation Certificate',
    description: 'Modern certificate for training program participation',
    category: CertificateCategory.PARTICIPATION,
    tags: ['participation', 'training', 'program', 'modern', 'development'],
    status: 'open' as const,
    preview: '/templates/participation-4.png',
    backgroundImage: '/templates/participation-4.png',
    orientation: 'portrait' as const,
    aspectRatio: 3/4,
    seoTitle: 'Training Participation Certificate Template',
    seoDescription: 'Create modern participation certificates for training programs.',
    seoKeywords: ['training participation certificate', 'program certificate template'],
    style: {
      background: '#FFFFFF',
      border: '3pt solid #7C3AED',
      colors: { primary: '#7C3AED', secondary: '#A855F7', background: '#FFFFFF', text: '#1F2937', border: '#7C3AED' },
      fonts: {
        name: { family: 'Playfair Display', size: 32, weight: 600, color: '#1F2937' },
        body: { family: 'Inter', size: 14, weight: 400, color: '#374151' },
        signature: { family: 'Dancing Script', size: 24, weight: 400, color: '#1F2937' }
      }
    },
    layout: {
      name: { x: 300, y: 350, width: 400, height: 50, align: 'center', fontSize: 32, fontFamily: 'Playfair Display', color: '#1F2937', fontWeight: 600 },
      details: { x: 300, y: 450, width: 400, height: 100, align: 'center', fontSize: 16, fontFamily: 'Inter', color: '#374151', fontWeight: 400 },
      date: { x: 100, y: 800, width: 150, height: 30, align: 'left', fontSize: 14, fontFamily: 'Inter', color: '#374151', fontWeight: 400 },
      signature: { x: 500, y: 800, width: 150, height: 30, align: 'right', fontSize: 24, fontFamily: 'Dancing Script', color: '#1F2937', fontWeight: 400 }
    },
    constraints: { nameMaxLength: 50, nameMinLength: 1, dateMaxLength: 20, dateMinLength: 1, signatureMaxLength: 30, signatureMinLength: 1, detailsMaxLength: 200, detailsMinLength: 10 },
    validation: { namePattern: /^[a-zA-Z\s\-.\']+$/, datePattern: /^[a-zA-Z0-9\s,\-.]+$/, signaturePattern: /^[a-zA-Z\s\-.\']+$/, detailsPattern: /^[a-zA-Z0-9\s,.!?\-\'"()]+$/ }
  },

  // Participation Category Template 5
  {
    id: 'participation-template-5',
    name: 'participation-template-5',
    displayName: 'Webinar Participation Certificate',
    description: 'Digital-friendly certificate for webinar participation',
    category: CertificateCategory.PARTICIPATION,
    tags: ['participation', 'webinar', 'digital', 'online', 'learning'],
    status: 'open' as const,
    preview: '/templates/participation-1.png',
    backgroundImage: '/templates/participation-1.png',
    orientation: 'portrait' as const,
    aspectRatio: 3/4,
    seoTitle: 'Webinar Participation Certificate Template',
    seoDescription: 'Create digital participation certificates for webinars and online events.',
    seoKeywords: ['webinar participation certificate', 'online event certificate template'],
    style: {
      background: '#FFFFFF',
      border: '3pt solid #0891B2',
      colors: { primary: '#0891B2', secondary: '#06B6D4', background: '#FFFFFF', text: '#1F2937', border: '#0891B2' },
      fonts: {
        name: { family: 'Inter', size: 28, weight: 600, color: '#1F2937' },
        body: { family: 'Inter', size: 14, weight: 400, color: '#374151' },
        signature: { family: 'Inter', size: 18, weight: 500, color: '#1F2937' }
      }
    },
    layout: {
      name: { x: 300, y: 350, width: 400, height: 50, align: 'center', fontSize: 28, fontFamily: 'Inter', color: '#1F2937', fontWeight: 600 },
      details: { x: 300, y: 450, width: 400, height: 100, align: 'center', fontSize: 16, fontFamily: 'Inter', color: '#374151', fontWeight: 400 },
      date: { x: 100, y: 800, width: 150, height: 30, align: 'left', fontSize: 14, fontFamily: 'Inter', color: '#374151', fontWeight: 400 },
      signature: { x: 500, y: 800, width: 150, height: 30, align: 'right', fontSize: 18, fontFamily: 'Inter', color: '#1F2937', fontWeight: 500 }
    },
    constraints: { nameMaxLength: 50, nameMinLength: 1, dateMaxLength: 20, dateMinLength: 1, signatureMaxLength: 30, signatureMinLength: 1, detailsMaxLength: 200, detailsMinLength: 10 },
    validation: { namePattern: /^[a-zA-Z\s\-.\']+$/, datePattern: /^[a-zA-Z0-9\s,\-.]+$/, signaturePattern: /^[a-zA-Z\s\-.\']+$/, detailsPattern: /^[a-zA-Z0-9\s,.!?\-\'"()]+$/ }
  },
  {
    id: 'participation-template-6',
    name: 'participation-template-6',
    displayName: 'Event Participation Certificate',
    description: 'Professional certificate for event and workshop participation',

    // 分类信息
    category: CertificateCategory.PARTICIPATION,
    tags: ['participation', 'event', 'workshop', 'professional', 'modern'],
    status: 'open' as const,

    // 视觉属性
    preview: '/templates/participation-1.png',
    backgroundImage: '/templates/participation-1.png',
    orientation: 'portrait' as const,
    aspectRatio: 3/4,

    // SEO属性
    seoTitle: 'Event Participation Certificate Template | Workshop & Conference Certificates',
    seoDescription: 'Create professional participation certificates for events, workshops, and conferences. Perfect for acknowledging attendance and engagement.',
    seoKeywords: ['event participation certificate', 'workshop certificate template', 'conference participation', 'professional event certificate'],
    style: {
      background: '#FFFFFF',
      border: '2pt solid #333333',
      colors: {
        primary: '#333333',
        secondary: '#666666',
        background: '#FFFFFF',
        text: '#000000',
        border: '#333333',
      },
      fonts: {
        
        name: {
          family: 'Arial',
          size: 24,
          weight: 600,
          color: '#000000',
        },
        body: {
          family: 'Arial',
          size: 14,
          weight: 400,
          color: '#333333',
        },
        signature: {
          family: 'Arial',
          size: 20,
          weight: 400,
          color: '#000000',
        },
      },
    },
    layout: {
      name: {
        x: 300,
        y: 350,
        width: 400,
        height: 40,
        align: 'center' as const,
        fontSize: 24,
        fontFamily: 'Arial',
        color: '#000000',
        fontWeight: 600,
      },
      date: {
        x: 100,
        y: 800,
        width: 200,
        height: 30,
        align: 'left' as const,
        fontSize: 14,
        fontFamily: 'Arial',
        color: '#333333',
      },
      signature: {
        x: 500,
        y: 800,
        width: 200,
        height: 30,
        align: 'right' as const,
        fontSize: 20,
        fontFamily: 'Arial',
        color: '#000000',
      },
      details: {
        x: 300,
        y: 450,
        width: 400,
        height: 100,
        align: 'center' as const,
        fontSize: 14,
        fontFamily: 'Arial',
        color: '#333333',
      },
    },
    constraints: {
      nameMaxLength: 50,
      nameMinLength: 1,
      dateMaxLength: 20,
      dateMinLength: 1,
      signatureMaxLength: 30,
      signatureMinLength: 1,
      detailsMaxLength: 200,
      detailsMinLength: 10,
    },
    validation: {
      namePattern: /^[a-zA-Z\s\-\.\']+$/,
      datePattern: /^[a-zA-Z0-9\s\,\-\.]+$/,
      signaturePattern: /^[a-zA-Z\s\-\.\']+$/,
      detailsPattern: /^[a-zA-Z0-9\s\,\.\!\?\-\'\"\(\)]+$/,
    },
  },

  // Excellence Category Template 1
  {
    id: 'excellence-template-1',
    name: 'excellence-template-1',
    displayName: 'Outstanding Excellence Certificate',
    description: 'Premium certificate for outstanding performance and excellence',

    // 分类信息
    category: CertificateCategory.EXCELLENCE,
    tags: ['excellence', 'outstanding', 'premium', 'performance', 'merit'],
    status: 'open' as const,

    // 视觉属性
    preview: '/templates/2 - Copy.png',
    backgroundImage: '/templates/2 - Copy.png',
    orientation: 'landscape' as const,
    aspectRatio: 4/3,

    // SEO属性
    seoTitle: 'Outstanding Excellence Certificate Template | Premium Performance Awards',
    seoDescription: 'Create premium excellence certificates for outstanding performance. Perfect for recognizing exceptional achievements and merit awards.',
    seoKeywords: ['outstanding excellence certificate', 'premium performance award', 'merit certificate template', 'exceptional achievement certificate'],
    style: {
      background: '#FFFFFF',
      border: '2pt solid #333333',
      colors: {
        primary: '#333333',
        secondary: '#666666',
        background: '#FFFFFF',
        text: '#000000',
        border: '#333333',
      },
      fonts: {
        
        name: {
          family: 'Arial',
          size: 24,
          weight: 600,
          color: '#000000',
        },
        body: {
          family: 'Arial',
          size: 14,
          weight: 400,
          color: '#333333',
        },
        signature: {
          family: 'Arial',
          size: 20,
          weight: 400,
          color: '#000000',
        },
      },
    },
    layout: {
      name: {
        x: 400,
        y: 350,
        width: 400,
        height: 40,
        align: 'center' as const,
        fontSize: 24,
        fontFamily: 'Arial',
        color: '#000000',
        fontWeight: 600,
      },
      date: {
        x: 150,
        y: 150,
        width: 200,
        height: 30,
        align: 'left' as const,
        fontSize: 14,
        fontFamily: 'Arial',
        color: '#333333',
      },
      signature: {
        x: 650,
        y: 150,
        width: 200,
        height: 30,
        align: 'right' as const,
        fontSize: 20,
        fontFamily: 'Arial',
        color: '#000000',
      },
      details: {
        x: 200,
        y: 250,
        width: 600,
        height: 80,
        align: 'center' as const,
        fontSize: 14,
        fontFamily: 'Arial',
        color: '#333333',
      },
    },
    constraints: {
      nameMaxLength: 50,
      nameMinLength: 1,
      dateMaxLength: 20,
      dateMinLength: 1,
      signatureMaxLength: 30,
      signatureMinLength: 1,
      detailsMaxLength: 200,
      detailsMinLength: 10,
    },
    validation: {
      namePattern: /^[a-zA-Z\s\-\.\']+$/,
      datePattern: /^[a-zA-Z0-9\s\,\-\.]+$/,
      signaturePattern: /^[a-zA-Z\s\-\.\']+$/,
      detailsPattern: /^[a-zA-Z0-9\s\,\.\!\?\-\'\"\(\)]+$/,
    },
  },

  // Excellence Category Template 2
  {
    id: 'excellence-template-2',
    name: 'excellence-template-2',
    displayName: 'Distinguished Excellence Certificate',
    description: 'Distinguished certificate for exceptional performance and excellence',

    // 分类信息
    category: CertificateCategory.EXCELLENCE,
    tags: ['excellence', 'distinguished', 'exceptional', 'premium', 'sophisticated'],
    status: 'open' as const,

    // 视觉属性
    preview: '/templates/4 - Copy.jpg',
    backgroundImage: '/templates/4 - Copy.jpg',
    orientation: 'landscape' as const,
    aspectRatio: 4/3,

    // SEO属性
    seoTitle: 'Distinguished Excellence Certificate Template | Exceptional Performance Awards',
    seoDescription: 'Create distinguished excellence certificates for exceptional performance. Perfect for recognizing top performers and sophisticated achievements.',
    seoKeywords: ['distinguished excellence certificate', 'exceptional performance award', 'sophisticated certificate template', 'premium excellence recognition'],
    style: {
      background: '#FFFFFF',
      border: '2pt solid #333333',
      colors: {
        primary: '#333333',
        secondary: '#666666',
        background: '#FFFFFF',
        text: '#000000',
        border: '#333333',
      },
      fonts: {
        
        name: {
          family: 'Arial',
          size: 24,
          weight: 600,
          color: '#000000',
        },
        body: {
          family: 'Arial',
          size: 14,
          weight: 400,
          color: '#333333',
        },
        signature: {
          family: 'Arial',
          size: 20,
          weight: 400,
          color: '#000000',
        },
      },
    },
    layout: {
      name: {
        x: 400,
        y: 350,
        width: 400,
        height: 40,
        align: 'center' as const,
        fontSize: 24,
        fontFamily: 'Arial',
        color: '#000000',
        fontWeight: 600,
      },
      date: {
        x: 150,
        y: 150,
        width: 200,
        height: 30,
        align: 'left' as const,
        fontSize: 14,
        fontFamily: 'Arial',
        color: '#333333',
      },
      signature: {
        x: 650,
        y: 150,
        width: 200,
        height: 30,
        align: 'right' as const,
        fontSize: 20,
        fontFamily: 'Arial',
        color: '#000000',
      },
      details: {
        x: 200,
        y: 250,
        width: 600,
        height: 80,
        align: 'center' as const,
        fontSize: 14,
        fontFamily: 'Arial',
        color: '#333333',
      },
    },
    constraints: {
      nameMaxLength: 50,
      nameMinLength: 1,
      dateMaxLength: 20,
      dateMinLength: 1,
      signatureMaxLength: 30,
      signatureMinLength: 1,
      detailsMaxLength: 200,
      detailsMinLength: 10,
    },
    validation: {
      namePattern: /^[a-zA-Z\s\-\.\']+$/,
      datePattern: /^[a-zA-Z0-9\s\,\-\.]+$/,
      signaturePattern: /^[a-zA-Z\s\-\.\']+$/,
      detailsPattern: /^[a-zA-Z0-9\s\,\.\!\?\-\'\"\(\)]+$/,
    },
  },

  // Excellence Category Template 3
  {
    id: 'excellence-template-3',
    name: 'excellence-template-3',
    displayName: 'Superior Excellence Certificate',
    description: 'Superior design for exceptional excellence recognition',
    category: CertificateCategory.EXCELLENCE,
    tags: ['excellence', 'superior', 'exceptional', 'premium', 'recognition'],
    status: 'open' as const,
    preview: '/templates/1.png',
    backgroundImage: '/templates/1.png',
    orientation: 'landscape' as const,
    aspectRatio: 4/3,
    seoTitle: 'Superior Excellence Certificate Template',
    seoDescription: 'Create superior excellence certificates for exceptional achievements.',
    seoKeywords: ['superior excellence certificate', 'exceptional achievement certificate'],
    style: {
      background: '#FFFFFF',
      border: '3pt solid #F59E0B',
      colors: { primary: '#F59E0B', secondary: '#FCD34D', background: '#FFFFFF', text: '#1F2937', border: '#F59E0B' },
      fonts: {
        name: { family: 'Playfair Display', size: 32, weight: 600, color: '#1F2937' },
        body: { family: 'Inter', size: 14, weight: 400, color: '#374151' },
        signature: { family: 'Dancing Script', size: 24, weight: 400, color: '#1F2937' }
      }
    },
    layout: {
      name: { x: 400, y: 300, width: 400, height: 50, align: 'center', fontSize: 32, fontFamily: 'Playfair Display', color: '#1F2937', fontWeight: 600 },
      details: { x: 400, y: 350, width: 500, height: 80, align: 'center', fontSize: 16, fontFamily: 'Inter', color: '#374151', fontWeight: 400 },
      date: { x: 150, y: 650, width: 150, height: 30, align: 'left', fontSize: 14, fontFamily: 'Inter', color: '#374151', fontWeight: 400 },
      signature: { x: 445, y: 650, width: 150, height: 30, align: 'right', fontSize: 24, fontFamily: 'Dancing Script', color: '#1F2937', fontWeight: 400 }
    },
    constraints: { nameMaxLength: 50, nameMinLength: 1, dateMaxLength: 20, dateMinLength: 1, signatureMaxLength: 30, signatureMinLength: 1, detailsMaxLength: 200, detailsMinLength: 10 },
    validation: { namePattern: /^[a-zA-Z\s\-.\']+$/, datePattern: /^[a-zA-Z0-9\s,\-.]+$/, signaturePattern: /^[a-zA-Z\s\-.\']+$/, detailsPattern: /^[a-zA-Z0-9\s,.!?\-\'"()]+$/ }
  },

  // Excellence Category Template 4
  {
    id: 'excellence-template-4',
    name: 'excellence-template-4',
    displayName: 'Distinguished Excellence Certificate',
    description: 'Distinguished design for highest level excellence',
    category: CertificateCategory.EXCELLENCE,
    tags: ['excellence', 'distinguished', 'highest', 'premium', 'elite'],
    status: 'open' as const,
    preview: '/templates/3.jpg',
    backgroundImage: '/templates/3.jpg',
    orientation: 'landscape' as const,
    aspectRatio: 4/3,
    seoTitle: 'Distinguished Excellence Certificate Template',
    seoDescription: 'Create distinguished excellence certificates for highest achievements.',
    seoKeywords: ['distinguished excellence certificate', 'elite achievement certificate'],
    style: {
      background: '#FFFFFF',
      border: '3pt solid #7C2D12',
      colors: { primary: '#7C2D12', secondary: '#DC2626', background: '#FFFFFF', text: '#1F2937', border: '#7C2D12' },
      fonts: {
        name: { family: 'Playfair Display', size: 32, weight: 600, color: '#1F2937' },
        body: { family: 'Inter', size: 14, weight: 400, color: '#374151' },
        signature: { family: 'Dancing Script', size: 24, weight: 400, color: '#1F2937' }
      }
    },
    layout: {
      name: { x: 400, y: 300, width: 400, height: 50, align: 'center', fontSize: 32, fontFamily: 'Playfair Display', color: '#1F2937', fontWeight: 600 },
      details: { x: 400, y: 350, width: 500, height: 80, align: 'center', fontSize: 16, fontFamily: 'Inter', color: '#374151', fontWeight: 400 },
      date: { x: 150, y: 650, width: 150, height: 30, align: 'left', fontSize: 14, fontFamily: 'Inter', color: '#374151', fontWeight: 400 },
      signature: { x: 445, y: 650, width: 150, height: 30, align: 'right', fontSize: 24, fontFamily: 'Dancing Script', color: '#1F2937', fontWeight: 400 }
    },
    constraints: { nameMaxLength: 50, nameMinLength: 1, dateMaxLength: 20, dateMinLength: 1, signatureMaxLength: 30, signatureMinLength: 1, detailsMaxLength: 200, detailsMinLength: 10 },
    validation: { namePattern: /^[a-zA-Z\s\-.\']+$/, datePattern: /^[a-zA-Z0-9\s,\-.]+$/, signaturePattern: /^[a-zA-Z\s\-.\']+$/, detailsPattern: /^[a-zA-Z0-9\s,.!?\-\'"()]+$/ }
  },

  // Excellence Category Template 5
  {
    id: 'excellence-template-5',
    name: 'excellence-template-5',
    displayName: 'Platinum Excellence Certificate',
    description: 'Platinum-level design for ultimate excellence recognition',
    category: CertificateCategory.EXCELLENCE,
    tags: ['excellence', 'platinum', 'ultimate', 'luxury', 'prestige'],
    status: 'open' as const,
    preview: '/templates/2 - Copy.png',
    backgroundImage: '/templates/2 - Copy.png',
    orientation: 'landscape' as const,
    aspectRatio: 4/3,
    seoTitle: 'Platinum Excellence Certificate Template',
    seoDescription: 'Create platinum excellence certificates for ultimate achievements.',
    seoKeywords: ['platinum excellence certificate', 'luxury achievement certificate'],
    style: {
      background: '#FFFFFF',
      border: '3pt solid #6B7280',
      colors: { primary: '#6B7280', secondary: '#9CA3AF', background: '#FFFFFF', text: '#1F2937', border: '#6B7280' },
      fonts: {
        name: { family: 'Playfair Display', size: 32, weight: 600, color: '#1F2937' },
        body: { family: 'Inter', size: 14, weight: 400, color: '#374151' },
        signature: { family: 'Dancing Script', size: 24, weight: 400, color: '#1F2937' }
      }
    },
    layout: {
      name: { x: 400, y: 300, width: 400, height: 50, align: 'center', fontSize: 32, fontFamily: 'Playfair Display', color: '#1F2937', fontWeight: 600 },
      details: { x: 400, y: 350, width: 500, height: 80, align: 'center', fontSize: 16, fontFamily: 'Inter', color: '#374151', fontWeight: 400 },
      date: { x: 150, y: 650, width: 150, height: 30, align: 'left', fontSize: 14, fontFamily: 'Inter', color: '#374151', fontWeight: 400 },
      signature: { x: 445, y: 650, width: 150, height: 30, align: 'right', fontSize: 24, fontFamily: 'Dancing Script', color: '#1F2937', fontWeight: 400 }
    },
    constraints: { nameMaxLength: 50, nameMinLength: 1, dateMaxLength: 20, dateMinLength: 1, signatureMaxLength: 30, signatureMinLength: 1, detailsMaxLength: 200, detailsMinLength: 10 },
    validation: { namePattern: /^[a-zA-Z\s\-.\']+$/, datePattern: /^[a-zA-Z0-9\s,\-.]+$/, signaturePattern: /^[a-zA-Z\s\-.\']+$/, detailsPattern: /^[a-zA-Z0-9\s,.!?\-\'"()]+$/ }
  },

  // ==================== 奖励类证书模板 ====================
  // Award Certificate Template 1 - Professional Gold Design
  {
    id: 'award-template-1',
    name: 'award-template-1',
    displayName: 'Professional Gold Award Certificate',
    description: 'Elegant gold-themed award certificate for professional recognition',

    // 分类信息
    category: CertificateCategory.AWARD_CERTIFICATE,
    tags: ['award', 'professional', 'gold', 'elegant', 'recognition', 'business'],
    status: 'open' as const,

    // 视觉属性
    preview: '/templates/award1.png',
    backgroundImage: '/templates/award1-blank.png',
    orientation: 'landscape' as const,
    aspectRatio: 4/3,

    // SEO属性
    seoTitle: 'Professional Gold Award Certificate Template | Business Recognition Awards',
    seoDescription: 'Create stunning gold award certificates for professional recognition. Perfect for employee awards, business achievements, and corporate recognition programs.',
    seoKeywords: ['professional award certificate template', 'gold award certificate', 'business recognition award', 'employee award certificate', 'corporate award template'],

    // 样式配置
    style: {
      background: '#FFFBF0',
      border: '3pt solid #D97706',
      colors: {
        primary: '#D97706',
        secondary: '#F59E0B',
        background: '#FFFBF0',
        text: '#1F2937',
        border: '#D97706',
      },
      fonts: {
        name: {
          family: 'Playfair Display',
          size: 36,
          weight: 700,
          color: '#1F2937',
        },
        body: {
          family: 'Inter',
          size: 16,
          weight: 400,
          color: '#374151',
        },
        signature: {
          family: 'Dancing Script',
          size: 28,
          weight: 400,
          color: '#1F2937',
        },
      },
    },

    // 布局配置
    layout: {
      name: {
        x: 421,
        y: 320,
        width: 400,
        height: 50,
        align: 'center',
        fontSize: 36,
        fontFamily: 'Playfair Display',
        color: '#1F2937',
        fontWeight: 700,
      },
      details: {
        x: 421,
        y: 380,
        width: 500,
        height: 80,
        align: 'center',
        fontSize: 18,
        fontFamily: 'Inter',
        color: '#374151',
        fontWeight: 400,
      },
      date: {
        x: 150,
        y: 500,
        width: 150,
        height: 30,
        align: 'left',
        fontSize: 16,
        fontFamily: 'Inter',
        color: '#374151',
        fontWeight: 400,
      },
      signature: {
        x: 692,
        y: 500,
        width: 150,
        height: 30,
        align: 'right',
        fontSize: 28,
        fontFamily: 'Dancing Script',
        color: '#1F2937',
        fontWeight: 400,
      },
    },
    constraints: {
      nameMaxLength: 50,
      nameMinLength: 1,
      dateMaxLength: 20,
      dateMinLength: 1,
      signatureMaxLength: 30,
      signatureMinLength: 1,
      detailsMaxLength: 200,
      detailsMinLength: 10,
    },
    validation: {
      namePattern: /^[a-zA-Z\s\-\.\']+$/,
      datePattern: /^[a-zA-Z0-9\s\,\-\.]+$/,
      signaturePattern: /^[a-zA-Z\s\-\.\']+$/,
      detailsPattern: /^[a-zA-Z0-9\s\,\.\!\?\-\'\"\(\)]+$/,
    },
  },

  // Award Certificate Template 2 - Classic Blue Design
  {
    id: 'award-template-2',
    name: 'award-template-2',
    displayName: 'Classic Blue Award Certificate',
    description: 'Traditional blue award certificate with formal design elements',

    // 分类信息
    category: CertificateCategory.AWARD_CERTIFICATE,
    tags: ['award', 'classic', 'blue', 'formal', 'traditional', 'corporate'],
    status: 'open' as const,

    // 视觉属性
    preview: '/templates/award2.png',
    backgroundImage: '/templates/award2-blank.png',
    orientation: 'landscape' as const,
    aspectRatio: 4/3,

    // SEO属性
    seoTitle: 'Classic Blue Award Certificate Template | Traditional Corporate Awards',
    seoDescription: 'Create classic blue award certificates with traditional design. Perfect for corporate awards, academic recognition, and formal achievement ceremonies.',
    seoKeywords: ['classic award certificate template', 'blue award certificate', 'traditional award design', 'corporate award template', 'formal recognition certificate'],

    // 样式配置
    style: {
      background: '#F8FAFC',
      border: '3pt solid #1E40AF',
      colors: {
        primary: '#1E40AF',
        secondary: '#3B82F6',
        background: '#F8FAFC',
        text: '#1F2937',
        border: '#1E40AF',
      },
      fonts: {
        name: {
          family: 'EB Garamond',
          size: 34,
          weight: 600,
          color: '#1F2937',
        },
        body: {
          family: 'Inter',
          size: 16,
          weight: 400,
          color: '#374151',
        },
        signature: {
          family: 'EB Garamond',
          size: 24,
          weight: 500,
          color: '#1F2937',
        },
      },
    },

    // 布局配置
    layout: {
      name: {
        x: 421,
        y: 320,
        width: 400,
        height: 50,
        align: 'center',
        fontSize: 34,
        fontFamily: 'EB Garamond',
        color: '#1F2937',
        fontWeight: 600,
      },
      details: {
        x: 421,
        y: 380,
        width: 500,
        height: 80,
        align: 'center',
        fontSize: 18,
        fontFamily: 'Inter',
        color: '#374151',
        fontWeight: 400,
      },
      date: {
        x: 150,
        y: 500,
        width: 150,
        height: 30,
        align: 'left',
        fontSize: 16,
        fontFamily: 'Inter',
        color: '#374151',
        fontWeight: 400,
      },
      signature: {
        x: 692,
        y: 500,
        width: 150,
        height: 30,
        align: 'right',
        fontSize: 24,
        fontFamily: 'EB Garamond',
        color: '#1F2937',
        fontWeight: 500,
      },
    },
    constraints: {
      nameMaxLength: 50,
      nameMinLength: 1,
      dateMaxLength: 20,
      dateMinLength: 1,
      signatureMaxLength: 30,
      signatureMinLength: 1,
      detailsMaxLength: 200,
      detailsMinLength: 10,
    },
    validation: {
      namePattern: /^[a-zA-Z\s\-\.\']+$/,
      datePattern: /^[a-zA-Z0-9\s\,\-\.]+$/,
      signaturePattern: /^[a-zA-Z\s\-\.\']+$/,
      detailsPattern: /^[a-zA-Z0-9\s\,\.\!\?\-\'\"\(\)]+$/,
    },
  },

  // ==================== 表彰证书模板 ====================
  // Recognition Certificate Template 1 - Modern Purple Design
  {
    id: 'recognition-template-1',
    name: 'recognition-template-1',
    displayName: 'Modern Purple Recognition Certificate',
    description: 'Contemporary purple recognition certificate for outstanding contributions',

    // 分类信息
    category: CertificateCategory.RECOGNITION_CERTIFICATE,
    tags: ['recognition', 'modern', 'purple', 'contemporary', 'outstanding', 'contribution'],
    status: 'open' as const,

    // 视觉属性
    preview: '/templates/recognition1.png',
    backgroundImage: '/templates/recognition1-blank.png',
    orientation: 'landscape' as const,
    aspectRatio: 4/3,

    // SEO属性
    seoTitle: 'Modern Purple Recognition Certificate Template | Outstanding Contribution Awards',
    seoDescription: 'Create modern purple recognition certificates for outstanding contributions. Perfect for employee recognition, volunteer appreciation, and community service awards.',
    seoKeywords: ['recognition certificate template', 'purple recognition certificate', 'outstanding contribution award', 'employee recognition certificate', 'volunteer appreciation certificate'],

    // 样式配置
    style: {
      background: '#FEFBFF',
      border: '3pt solid #7C3AED',
      colors: {
        primary: '#7C3AED',
        secondary: '#A855F7',
        background: '#FEFBFF',
        text: '#1F2937',
        border: '#7C3AED',
      },
      fonts: {
        name: {
          family: 'Inter',
          size: 32,
          weight: 700,
          color: '#1F2937',
        },
        body: {
          family: 'Inter',
          size: 16,
          weight: 400,
          color: '#374151',
        },
        signature: {
          family: 'Inter',
          size: 20,
          weight: 600,
          color: '#1F2937',
        },
      },
    },

    // 布局配置
    layout: {
      name: {
        x: 421,
        y: 320,
        width: 400,
        height: 50,
        align: 'center',
        fontSize: 32,
        fontFamily: 'Inter',
        color: '#1F2937',
        fontWeight: 700,
      },
      details: {
        x: 421,
        y: 380,
        width: 500,
        height: 80,
        align: 'center',
        fontSize: 18,
        fontFamily: 'Inter',
        color: '#374151',
        fontWeight: 400,
      },
      date: {
        x: 150,
        y: 500,
        width: 150,
        height: 30,
        align: 'left',
        fontSize: 16,
        fontFamily: 'Inter',
        color: '#374151',
        fontWeight: 400,
      },
      signature: {
        x: 692,
        y: 500,
        width: 150,
        height: 30,
        align: 'right',
        fontSize: 20,
        fontFamily: 'Inter',
        color: '#1F2937',
        fontWeight: 600,
      },
    },
    constraints: {
      nameMaxLength: 50,
      nameMinLength: 1,
      dateMaxLength: 20,
      dateMinLength: 1,
      signatureMaxLength: 30,
      signatureMinLength: 1,
      detailsMaxLength: 200,
      detailsMinLength: 10,
    },
    validation: {
      namePattern: /^[a-zA-Z\s\-\.\']+$/,
      datePattern: /^[a-zA-Z0-9\s\,\-\.]+$/,
      signaturePattern: /^[a-zA-Z\s\-\.\']+$/,
      detailsPattern: /^[a-zA-Z0-9\s\,\.\!\?\-\'\"\(\)]+$/,
    },
  },

  // Recognition Certificate Template 2 - Elegant Green Design
  {
    id: 'recognition-template-2',
    name: 'recognition-template-2',
    displayName: 'Elegant Green Recognition Certificate',
    description: 'Sophisticated green recognition certificate for exceptional service',

    // 分类信息
    category: CertificateCategory.RECOGNITION_CERTIFICATE,
    tags: ['recognition', 'elegant', 'green', 'sophisticated', 'service', 'exceptional'],
    status: 'open' as const,

    // 视觉属性
    preview: '/templates/recognition2.png',
    backgroundImage: '/templates/recognition2-blank.png',
    orientation: 'landscape' as const,
    aspectRatio: 4/3,

    // SEO属性
    seoTitle: 'Elegant Green Recognition Certificate Template | Exceptional Service Awards',
    seoDescription: 'Create elegant green recognition certificates for exceptional service. Ideal for service awards, dedication recognition, and professional appreciation.',
    seoKeywords: ['green recognition certificate template', 'elegant recognition certificate', 'exceptional service award', 'dedication recognition certificate', 'professional appreciation award'],

    // 样式配置
    style: {
      background: '#F0FDF4',
      border: '3pt solid #059669',
      colors: {
        primary: '#059669',
        secondary: '#10B981',
        background: '#F0FDF4',
        text: '#1F2937',
        border: '#059669',
      },
      fonts: {
        name: {
          family: 'Playfair Display',
          size: 34,
          weight: 600,
          color: '#1F2937',
        },
        body: {
          family: 'Inter',
          size: 16,
          weight: 400,
          color: '#374151',
        },
        signature: {
          family: 'Dancing Script',
          size: 26,
          weight: 400,
          color: '#1F2937',
        },
      },
    },

    // 布局配置
    layout: {
      name: {
        x: 421,
        y: 320,
        width: 400,
        height: 50,
        align: 'center',
        fontSize: 34,
        fontFamily: 'Playfair Display',
        color: '#1F2937',
        fontWeight: 600,
      },
      details: {
        x: 421,
        y: 380,
        width: 500,
        height: 80,
        align: 'center',
        fontSize: 18,
        fontFamily: 'Inter',
        color: '#374151',
        fontWeight: 400,
      },
      date: {
        x: 150,
        y: 500,
        width: 150,
        height: 30,
        align: 'left',
        fontSize: 16,
        fontFamily: 'Inter',
        color: '#374151',
        fontWeight: 400,
      },
      signature: {
        x: 692,
        y: 500,
        width: 150,
        height: 30,
        align: 'right',
        fontSize: 26,
        fontFamily: 'Dancing Script',
        color: '#1F2937',
        fontWeight: 400,
      },
    },
    constraints: {
      nameMaxLength: 50,
      nameMinLength: 1,
      dateMaxLength: 20,
      dateMinLength: 1,
      signatureMaxLength: 30,
      signatureMinLength: 1,
      detailsMaxLength: 200,
      detailsMinLength: 10,
    },
    validation: {
      namePattern: /^[a-zA-Z\s\-\.\']+$/,
      datePattern: /^[a-zA-Z0-9\s\,\-\.]+$/,
      signaturePattern: /^[a-zA-Z\s\-\.\']+$/,
      detailsPattern: /^[a-zA-Z0-9\s\,\.\!\?\-\'\"\(\)]+$/,
    },
  },

  // ==================== 体育证书模板 ====================
  // Sports Certificate Template 1 - Dynamic Orange Design
  {
    id: 'sports-template-1',
    name: 'sports-template-1',
    displayName: 'Dynamic Orange Sports Certificate',
    description: 'Energetic orange sports certificate for athletic achievements',

    // 分类信息
    category: CertificateCategory.SPORTS_CERTIFICATE,
    tags: ['sports', 'athletic', 'orange', 'dynamic', 'energetic', 'achievement', 'competition'],
    status: 'open' as const,

    // 视觉属性
    preview: '/templates/sports1.png',
    backgroundImage: '/templates/sports1-blank.png',
    orientation: 'landscape' as const,
    aspectRatio: 4/3,

    // SEO属性
    seoTitle: 'Dynamic Orange Sports Certificate Template | Athletic Achievement Awards',
    seoDescription: 'Create dynamic orange sports certificates for athletic achievements. Perfect for basketball, football, soccer, swimming, and all sports competitions.',
    seoKeywords: ['sports certificate template', 'athletic achievement certificate', 'basketball certificate', 'football certificate', 'soccer certificate', 'swimming certificate', 'sports award template'],

    // 样式配置
    style: {
      background: '#FFF7ED',
      border: '3pt solid #EA580C',
      colors: {
        primary: '#EA580C',
        secondary: '#FB923C',
        background: '#FFF7ED',
        text: '#1F2937',
        border: '#EA580C',
      },
      fonts: {
        name: {
          family: 'Inter',
          size: 36,
          weight: 800,
          color: '#1F2937',
        },
        body: {
          family: 'Inter',
          size: 16,
          weight: 500,
          color: '#374151',
        },
        signature: {
          family: 'Inter',
          size: 22,
          weight: 600,
          color: '#1F2937',
        },
      },
    },

    // 布局配置
    layout: {
      name: {
        x: 421,
        y: 320,
        width: 400,
        height: 50,
        align: 'center',
        fontSize: 36,
        fontFamily: 'Inter',
        color: '#1F2937',
        fontWeight: 800,
      },
      details: {
        x: 421,
        y: 380,
        width: 500,
        height: 80,
        align: 'center',
        fontSize: 18,
        fontFamily: 'Inter',
        color: '#374151',
        fontWeight: 500,
      },
      date: {
        x: 150,
        y: 500,
        width: 150,
        height: 30,
        align: 'left',
        fontSize: 16,
        fontFamily: 'Inter',
        color: '#374151',
        fontWeight: 500,
      },
      signature: {
        x: 692,
        y: 500,
        width: 150,
        height: 30,
        align: 'right',
        fontSize: 22,
        fontFamily: 'Inter',
        color: '#1F2937',
        fontWeight: 600,
      },
    },
    constraints: {
      nameMaxLength: 50,
      nameMinLength: 1,
      dateMaxLength: 20,
      dateMinLength: 1,
      signatureMaxLength: 30,
      signatureMinLength: 1,
      detailsMaxLength: 200,
      detailsMinLength: 10,
    },
    validation: {
      namePattern: /^[a-zA-Z\s\-\.\']+$/,
      datePattern: /^[a-zA-Z0-9\s\,\-\.]+$/,
      signaturePattern: /^[a-zA-Z\s\-\.\']+$/,
      detailsPattern: /^[a-zA-Z0-9\s\,\.\!\?\-\'\"\(\)]+$/,
    },
  },

  // Sports Certificate Template 2 - Classic Red Design
  {
    id: 'sports-template-2',
    name: 'sports-template-2',
    displayName: 'Classic Red Sports Certificate',
    description: 'Traditional red sports certificate for championship victories',

    // 分类信息
    category: CertificateCategory.SPORTS_CERTIFICATE,
    tags: ['sports', 'championship', 'red', 'classic', 'traditional', 'victory', 'winner'],
    status: 'open' as const,

    // 视觉属性
    preview: '/templates/sports2.png',
    backgroundImage: '/templates/sports2-blank.png',
    orientation: 'landscape' as const,
    aspectRatio: 4/3,

    // SEO属性
    seoTitle: 'Classic Red Sports Certificate Template | Championship Victory Awards',
    seoDescription: 'Create classic red sports certificates for championship victories. Ideal for tournament winners, league champions, and sports competition awards.',
    seoKeywords: ['red sports certificate template', 'championship certificate', 'sports victory award', 'tournament winner certificate', 'league champion certificate', 'sports competition award'],

    // 样式配置
    style: {
      background: '#FEF2F2',
      border: '3pt solid #DC2626',
      colors: {
        primary: '#DC2626',
        secondary: '#EF4444',
        background: '#FEF2F2',
        text: '#1F2937',
        border: '#DC2626',
      },
      fonts: {
        name: {
          family: 'Playfair Display',
          size: 38,
          weight: 700,
          color: '#1F2937',
        },
        body: {
          family: 'Inter',
          size: 16,
          weight: 500,
          color: '#374151',
        },
        signature: {
          family: 'Dancing Script',
          size: 30,
          weight: 400,
          color: '#1F2937',
        },
      },
    },

    // 布局配置
    layout: {
      name: {
        x: 421,
        y: 320,
        width: 400,
        height: 50,
        align: 'center',
        fontSize: 38,
        fontFamily: 'Playfair Display',
        color: '#1F2937',
        fontWeight: 700,
      },
      details: {
        x: 421,
        y: 380,
        width: 500,
        height: 80,
        align: 'center',
        fontSize: 18,
        fontFamily: 'Inter',
        color: '#374151',
        fontWeight: 500,
      },
      date: {
        x: 150,
        y: 500,
        width: 150,
        height: 30,
        align: 'left',
        fontSize: 16,
        fontFamily: 'Inter',
        color: '#374151',
        fontWeight: 500,
      },
      signature: {
        x: 692,
        y: 500,
        width: 150,
        height: 30,
        align: 'right',
        fontSize: 30,
        fontFamily: 'Dancing Script',
        color: '#1F2937',
        fontWeight: 400,
      },
    },
    constraints: {
      nameMaxLength: 50,
      nameMinLength: 1,
      dateMaxLength: 20,
      dateMinLength: 1,
      signatureMaxLength: 30,
      signatureMinLength: 1,
      detailsMaxLength: 200,
      detailsMinLength: 10,
    },
    validation: {
      namePattern: /^[a-zA-Z\s\-\.\']+$/,
      datePattern: /^[a-zA-Z0-9\s\,\-\.]+$/,
      signaturePattern: /^[a-zA-Z\s\-\.\']+$/,
      detailsPattern: /^[a-zA-Z0-9\s\,\.\!\?\-\'\"\(\)]+$/,
    },
  },

  // ==================== 教育类证书模板 ====================
  // Graduation Certificate Template 1 - Academic Navy Design
  {
    id: 'graduation-template-1',
    name: 'graduation-template-1',
    displayName: 'Academic Navy Graduation Certificate',
    description: 'Formal navy graduation certificate for academic achievements',

    // 分类信息
    category: CertificateCategory.GRADUATION_CERTIFICATE,
    tags: ['graduation', 'academic', 'navy', 'formal', 'education', 'diploma', 'university'],
    status: 'open' as const,

    // 视觉属性
    preview: '/templates/graduation1.png',
    backgroundImage: '/templates/graduation1-blank.png',
    orientation: 'landscape' as const,
    aspectRatio: 4/3,

    // SEO属性
    seoTitle: 'Academic Navy Graduation Certificate Template | University Diploma Design',
    seoDescription: 'Create formal navy graduation certificates for academic achievements. Perfect for university diplomas, college degrees, and educational milestones.',
    seoKeywords: ['graduation certificate template', 'academic diploma certificate', 'university graduation certificate', 'college degree certificate', 'educational achievement certificate', 'formal graduation diploma'],

    // 样式配置
    style: {
      background: '#F8FAFC',
      border: '4pt solid #1E3A8A',
      colors: {
        primary: '#1E3A8A',
        secondary: '#3B82F6',
        background: '#F8FAFC',
        text: '#1F2937',
        border: '#1E3A8A',
      },
      fonts: {
        name: {
          family: 'EB Garamond',
          size: 40,
          weight: 600,
          color: '#1F2937',
        },
        body: {
          family: 'EB Garamond',
          size: 18,
          weight: 400,
          color: '#374151',
        },
        signature: {
          family: 'EB Garamond',
          size: 24,
          weight: 500,
          color: '#1F2937',
        },
      },
    },

    // 布局配置
    layout: {
      name: {
        x: 421,
        y: 320,
        width: 400,
        height: 50,
        align: 'center',
        fontSize: 40,
        fontFamily: 'EB Garamond',
        color: '#1F2937',
        fontWeight: 600,
      },
      details: {
        x: 421,
        y: 380,
        width: 500,
        height: 80,
        align: 'center',
        fontSize: 20,
        fontFamily: 'EB Garamond',
        color: '#374151',
        fontWeight: 400,
      },
      date: {
        x: 150,
        y: 500,
        width: 150,
        height: 30,
        align: 'left',
        fontSize: 18,
        fontFamily: 'EB Garamond',
        color: '#374151',
        fontWeight: 400,
      },
      signature: {
        x: 692,
        y: 500,
        width: 150,
        height: 30,
        align: 'right',
        fontSize: 24,
        fontFamily: 'EB Garamond',
        color: '#1F2937',
        fontWeight: 500,
      },
    },
    constraints: {
      nameMaxLength: 50,
      nameMinLength: 1,
      dateMaxLength: 20,
      dateMinLength: 1,
      signatureMaxLength: 30,
      signatureMinLength: 1,
      detailsMaxLength: 200,
      detailsMinLength: 10,
    },
    validation: {
      namePattern: /^[a-zA-Z\s\-\.\']+$/,
      datePattern: /^[a-zA-Z0-9\s\,\-\.]+$/,
      signaturePattern: /^[a-zA-Z\s\-\.\']+$/,
      detailsPattern: /^[a-zA-Z0-9\s\,\.\!\?\-\'\"\(\)]+$/,
    },
  },

  // Training Certificate Template 1 - Professional Teal Design
  {
    id: 'training-template-1',
    name: 'training-template-1',
    displayName: 'Professional Teal Training Certificate',
    description: 'Modern teal training certificate for professional development',

    // 分类信息
    category: CertificateCategory.TRAINING_CERTIFICATE,
    tags: ['training', 'professional', 'teal', 'modern', 'development', 'skills', 'workshop'],
    status: 'open' as const,

    // 视觉属性
    preview: '/templates/training1.png',
    backgroundImage: '/templates/training1-blank.png',
    orientation: 'landscape' as const,
    aspectRatio: 4/3,

    // SEO属性
    seoTitle: 'Professional Teal Training Certificate Template | Skills Development Awards',
    seoDescription: 'Create modern teal training certificates for professional development. Perfect for skill training, workshop completion, and professional certification programs.',
    seoKeywords: ['training certificate template', 'professional development certificate', 'skills training certificate', 'workshop completion certificate', 'professional certification template', 'training program certificate'],

    // 样式配置
    style: {
      background: '#F0FDFA',
      border: '3pt solid #0891B2',
      colors: {
        primary: '#0891B2',
        secondary: '#06B6D4',
        background: '#F0FDFA',
        text: '#1F2937',
        border: '#0891B2',
      },
      fonts: {
        name: {
          family: 'Inter',
          size: 34,
          weight: 600,
          color: '#1F2937',
        },
        body: {
          family: 'Inter',
          size: 16,
          weight: 400,
          color: '#374151',
        },
        signature: {
          family: 'Inter',
          size: 20,
          weight: 500,
          color: '#1F2937',
        },
      },
    },

    // 布局配置
    layout: {
      name: {
        x: 421,
        y: 320,
        width: 400,
        height: 50,
        align: 'center',
        fontSize: 34,
        fontFamily: 'Inter',
        color: '#1F2937',
        fontWeight: 600,
      },
      details: {
        x: 421,
        y: 380,
        width: 500,
        height: 80,
        align: 'center',
        fontSize: 18,
        fontFamily: 'Inter',
        color: '#374151',
        fontWeight: 400,
      },
      date: {
        x: 150,
        y: 500,
        width: 150,
        height: 30,
        align: 'left',
        fontSize: 16,
        fontFamily: 'Inter',
        color: '#374151',
        fontWeight: 400,
      },
      signature: {
        x: 692,
        y: 500,
        width: 150,
        height: 30,
        align: 'right',
        fontSize: 20,
        fontFamily: 'Inter',
        color: '#1F2937',
        fontWeight: 500,
      },
    },
    constraints: {
      nameMaxLength: 50,
      nameMinLength: 1,
      dateMaxLength: 20,
      dateMinLength: 1,
      signatureMaxLength: 30,
      signatureMinLength: 1,
      detailsMaxLength: 200,
      detailsMinLength: 10,
    },
    validation: {
      namePattern: /^[a-zA-Z\s\-\.\']+$/,
      datePattern: /^[a-zA-Z0-9\s\,\-\.]+$/,
      signaturePattern: /^[a-zA-Z\s\-\.\']+$/,
      detailsPattern: /^[a-zA-Z0-9\s\,\.\!\?\-\'\"\(\)]+$/,
    },
  },

  // ==================== 娱乐类证书模板 ====================
  // Funny Certificate Template 1 - Playful Pink Design
  {
    id: 'funny-template-1',
    name: 'funny-template-1',
    displayName: 'Playful Pink Funny Certificate',
    description: 'Fun and colorful pink certificate for humorous awards and jokes',

    // 分类信息
    category: CertificateCategory.FUNNY_CERTIFICATE,
    tags: ['funny', 'humorous', 'pink', 'playful', 'colorful', 'joke', 'entertainment', 'novelty'],
    status: 'open' as const,

    // 视觉属性
    preview: '/templates/funny1.png',
    backgroundImage: '/templates/funny1-blank.png',
    orientation: 'landscape' as const,
    aspectRatio: 4/3,

    // SEO属性
    seoTitle: 'Playful Pink Funny Certificate Template | Humorous Joke Awards',
    seoDescription: 'Create playful pink funny certificates for humorous awards and jokes. Perfect for office fun, party games, novelty gifts, and entertainment purposes.',
    seoKeywords: ['funny certificate template', 'humorous award certificate', 'joke certificate template', 'novelty certificate', 'entertainment certificate', 'playful award template', 'office fun certificate'],

    // 样式配置
    style: {
      background: '#FDF2F8',
      border: '3pt solid #EC4899',
      colors: {
        primary: '#EC4899',
        secondary: '#F472B6',
        background: '#FDF2F8',
        text: '#1F2937',
        border: '#EC4899',
      },
      fonts: {
        name: {
          family: 'Comic Neue',
          size: 36,
          weight: 700,
          color: '#1F2937',
        },
        body: {
          family: 'Comic Neue',
          size: 16,
          weight: 400,
          color: '#374151',
        },
        signature: {
          family: 'Dancing Script',
          size: 28,
          weight: 400,
          color: '#1F2937',
        },
      },
    },

    // 布局配置
    layout: {
      name: {
        x: 421,
        y: 320,
        width: 400,
        height: 50,
        align: 'center',
        fontSize: 36,
        fontFamily: 'Comic Neue',
        color: '#1F2937',
        fontWeight: 700,
      },
      details: {
        x: 421,
        y: 380,
        width: 500,
        height: 80,
        align: 'center',
        fontSize: 18,
        fontFamily: 'Comic Neue',
        color: '#374151',
        fontWeight: 400,
      },
      date: {
        x: 150,
        y: 500,
        width: 150,
        height: 30,
        align: 'left',
        fontSize: 16,
        fontFamily: 'Comic Neue',
        color: '#374151',
        fontWeight: 400,
      },
      signature: {
        x: 692,
        y: 500,
        width: 150,
        height: 30,
        align: 'right',
        fontSize: 28,
        fontFamily: 'Dancing Script',
        color: '#1F2937',
        fontWeight: 400,
      },
    },
    constraints: {
      nameMaxLength: 50,
      nameMinLength: 1,
      dateMaxLength: 20,
      dateMinLength: 1,
      signatureMaxLength: 30,
      signatureMinLength: 1,
      detailsMaxLength: 200,
      detailsMinLength: 10,
    },
    validation: {
      namePattern: /^[a-zA-Z\s\-\.\']+$/,
      datePattern: /^[a-zA-Z0-9\s\,\-\.]+$/,
      signaturePattern: /^[a-zA-Z\s\-\.\']+$/,
      detailsPattern: /^[a-zA-Z0-9\s\,\.\!\?\-\'\"\(\)]+$/,
    },
  },

  // Funny Certificate Template 2 - Bright Yellow Design
  {
    id: 'funny-template-2',
    name: 'funny-template-2',
    displayName: 'Bright Yellow Funny Certificate',
    description: 'Cheerful yellow certificate for amusing awards and gag gifts',

    // 分类信息
    category: CertificateCategory.FUNNY_CERTIFICATE,
    tags: ['funny', 'cheerful', 'yellow', 'bright', 'amusing', 'gag', 'gift', 'comedy'],
    status: 'open' as const,

    // 视觉属性
    preview: '/templates/funny2.png',
    backgroundImage: '/templates/funny2-blank.png',
    orientation: 'landscape' as const,
    aspectRatio: 4/3,

    // SEO属性
    seoTitle: 'Bright Yellow Funny Certificate Template | Amusing Gag Gift Awards',
    seoDescription: 'Create bright yellow funny certificates for amusing awards and gag gifts. Ideal for comedy events, office humor, and lighthearted recognition.',
    seoKeywords: ['yellow funny certificate template', 'amusing award certificate', 'gag gift certificate', 'comedy certificate template', 'lighthearted award', 'office humor certificate', 'cheerful novelty certificate'],

    // 样式配置
    style: {
      background: '#FFFBEB',
      border: '3pt solid #F59E0B',
      colors: {
        primary: '#F59E0B',
        secondary: '#FCD34D',
        background: '#FFFBEB',
        text: '#1F2937',
        border: '#F59E0B',
      },
      fonts: {
        name: {
          family: 'Comic Neue',
          size: 38,
          weight: 700,
          color: '#1F2937',
        },
        body: {
          family: 'Comic Neue',
          size: 16,
          weight: 400,
          color: '#374151',
        },
        signature: {
          family: 'Comic Neue',
          size: 24,
          weight: 600,
          color: '#1F2937',
        },
      },
    },

    // 布局配置
    layout: {
      name: {
        x: 421,
        y: 320,
        width: 400,
        height: 50,
        align: 'center',
        fontSize: 38,
        fontFamily: 'Comic Neue',
        color: '#1F2937',
        fontWeight: 700,
      },
      details: {
        x: 421,
        y: 380,
        width: 500,
        height: 80,
        align: 'center',
        fontSize: 18,
        fontFamily: 'Comic Neue',
        color: '#374151',
        fontWeight: 400,
      },
      date: {
        x: 150,
        y: 500,
        width: 150,
        height: 30,
        align: 'left',
        fontSize: 16,
        fontFamily: 'Comic Neue',
        color: '#374151',
        fontWeight: 400,
      },
      signature: {
        x: 692,
        y: 500,
        width: 150,
        height: 30,
        align: 'right',
        fontSize: 24,
        fontFamily: 'Comic Neue',
        color: '#1F2937',
        fontWeight: 600,
      },
    },
    constraints: {
      nameMaxLength: 50,
      nameMinLength: 1,
      dateMaxLength: 20,
      dateMinLength: 1,
      signatureMaxLength: 30,
      signatureMinLength: 1,
      detailsMaxLength: 200,
      detailsMinLength: 10,
    },
    validation: {
      namePattern: /^[a-zA-Z\s\-\.\']+$/,
      datePattern: /^[a-zA-Z0-9\s\,\-\.]+$/,
      signaturePattern: /^[a-zA-Z\s\-\.\']+$/,
      detailsPattern: /^[a-zA-Z0-9\s\,\.\!\?\-\'\"\(\)]+$/,
    },
  },

  // ==================== 专业类证书模板 ====================
  // Employee Certificate Template 1 - Corporate Blue Design
  {
    id: 'employee-template-1',
    name: 'employee-template-1',
    displayName: 'Corporate Blue Employee Certificate',
    description: 'Professional blue employee certificate for workplace recognition',

    // 分类信息
    category: CertificateCategory.EMPLOYEE_CERTIFICATE,
    tags: ['employee', 'corporate', 'blue', 'professional', 'workplace', 'recognition', 'business'],
    status: 'open' as const,

    // 视觉属性
    preview: '/templates/employee1.png',
    backgroundImage: '/templates/employee1-blank.png',
    orientation: 'landscape' as const,
    aspectRatio: 4/3,

    // SEO属性
    seoTitle: 'Corporate Blue Employee Certificate Template | Workplace Recognition Awards',
    seoDescription: 'Create professional blue employee certificates for workplace recognition. Perfect for employee of the month, service awards, and corporate appreciation programs.',
    seoKeywords: ['employee certificate template', 'corporate recognition certificate', 'workplace award certificate', 'employee of the month certificate', 'service award certificate', 'business appreciation certificate'],

    // 样式配置
    style: {
      background: '#F8FAFC',
      border: '3pt solid #1E40AF',
      colors: {
        primary: '#1E40AF',
        secondary: '#3B82F6',
        background: '#F8FAFC',
        text: '#1F2937',
        border: '#1E40AF',
      },
      fonts: {
        name: {
          family: 'Inter',
          size: 32,
          weight: 600,
          color: '#1F2937',
        },
        body: {
          family: 'Inter',
          size: 16,
          weight: 400,
          color: '#374151',
        },
        signature: {
          family: 'Inter',
          size: 20,
          weight: 500,
          color: '#1F2937',
        },
      },
    },

    // 布局配置
    layout: {
      name: {
        x: 421,
        y: 320,
        width: 400,
        height: 50,
        align: 'center',
        fontSize: 32,
        fontFamily: 'Inter',
        color: '#1F2937',
        fontWeight: 600,
      },
      details: {
        x: 421,
        y: 380,
        width: 500,
        height: 80,
        align: 'center',
        fontSize: 18,
        fontFamily: 'Inter',
        color: '#374151',
        fontWeight: 400,
      },
      date: {
        x: 150,
        y: 500,
        width: 150,
        height: 30,
        align: 'left',
        fontSize: 16,
        fontFamily: 'Inter',
        color: '#374151',
        fontWeight: 400,
      },
      signature: {
        x: 692,
        y: 500,
        width: 150,
        height: 30,
        align: 'right',
        fontSize: 20,
        fontFamily: 'Inter',
        color: '#1F2937',
        fontWeight: 500,
      },
    },
    constraints: {
      nameMaxLength: 50,
      nameMinLength: 1,
      dateMaxLength: 20,
      dateMinLength: 1,
      signatureMaxLength: 30,
      signatureMinLength: 1,
      detailsMaxLength: 200,
      detailsMinLength: 10,
    },
    validation: {
      namePattern: /^[a-zA-Z\s\-\.\']+$/,
      datePattern: /^[a-zA-Z0-9\s\,\-\.]+$/,
      signaturePattern: /^[a-zA-Z\s\-\.\']+$/,
      detailsPattern: /^[a-zA-Z0-9\s\,\.\!\?\-\'\"\(\)]+$/,
    },
  },

  // Volunteer Certificate Template 1 - Warm Orange Design
  {
    id: 'volunteer-template-1',
    name: 'volunteer-template-1',
    displayName: 'Warm Orange Volunteer Certificate',
    description: 'Heartwarming orange volunteer certificate for community service recognition',

    // 分类信息
    category: CertificateCategory.VOLUNTEER_CERTIFICATE,
    tags: ['volunteer', 'community', 'orange', 'warm', 'service', 'appreciation', 'nonprofit'],
    status: 'open' as const,

    // 视觉属性
    preview: '/templates/volunteer1.png',
    backgroundImage: '/templates/volunteer1-blank.png',
    orientation: 'landscape' as const,
    aspectRatio: 4/3,

    // SEO属性
    seoTitle: 'Warm Orange Volunteer Certificate Template | Community Service Recognition',
    seoDescription: 'Create warm orange volunteer certificates for community service recognition. Perfect for nonprofit organizations, charity work, and volunteer appreciation programs.',
    seoKeywords: ['volunteer certificate template', 'community service certificate', 'nonprofit recognition certificate', 'charity volunteer certificate', 'volunteer appreciation award', 'community service award'],

    // 样式配置
    style: {
      background: '#FFF7ED',
      border: '3pt solid #EA580C',
      colors: {
        primary: '#EA580C',
        secondary: '#FB923C',
        background: '#FFF7ED',
        text: '#1F2937',
        border: '#EA580C',
      },
      fonts: {
        name: {
          family: 'Playfair Display',
          size: 34,
          weight: 600,
          color: '#1F2937',
        },
        body: {
          family: 'Inter',
          size: 16,
          weight: 400,
          color: '#374151',
        },
        signature: {
          family: 'Dancing Script',
          size: 26,
          weight: 400,
          color: '#1F2937',
        },
      },
    },

    // 布局配置
    layout: {
      name: {
        x: 421,
        y: 320,
        width: 400,
        height: 50,
        align: 'center',
        fontSize: 34,
        fontFamily: 'Playfair Display',
        color: '#1F2937',
        fontWeight: 600,
      },
      details: {
        x: 421,
        y: 380,
        width: 500,
        height: 80,
        align: 'center',
        fontSize: 18,
        fontFamily: 'Inter',
        color: '#374151',
        fontWeight: 400,
      },
      date: {
        x: 150,
        y: 500,
        width: 150,
        height: 30,
        align: 'left',
        fontSize: 16,
        fontFamily: 'Inter',
        color: '#374151',
        fontWeight: 400,
      },
      signature: {
        x: 692,
        y: 500,
        width: 150,
        height: 30,
        align: 'right',
        fontSize: 26,
        fontFamily: 'Dancing Script',
        color: '#1F2937',
        fontWeight: 400,
      },
    },
    constraints: {
      nameMaxLength: 50,
      nameMinLength: 1,
      dateMaxLength: 20,
      dateMinLength: 1,
      signatureMaxLength: 30,
      signatureMinLength: 1,
      detailsMaxLength: 200,
      detailsMinLength: 10,
    },
    validation: {
      namePattern: /^[a-zA-Z\s\-\.\']+$/,
      datePattern: /^[a-zA-Z0-9\s\,\-\.]+$/,
      signaturePattern: /^[a-zA-Z\s\-\.\']+$/,
      detailsPattern: /^[a-zA-Z0-9\s\,\.\!\?\-\'\"\(\)]+$/,
    },
  }
];



/**
 * 获取默认模板
 */
export function getDefaultTemplate(): CertificateTemplate {
  return CERTIFICATE_TEMPLATES[0];
}

/**
 * 获取所有模板ID
 */
export function getAllTemplateIds(): string[] {
  return CERTIFICATE_TEMPLATES.map(template => template.id);
}

/**
 * 验证模板ID是否有效
 */
export function isValidTemplateId(id: string): boolean {
  return CERTIFICATE_TEMPLATES.some(template => template.id === id);
}

/**
 * 模板分类映射配置
 * 用于将现有模板分配到不同分类
 */
export const TEMPLATE_CATEGORY_MAPPING = {
  // Achievement (成就证书)
  'achievement-template-1': CertificateCategory.ACHIEVEMENT,
  'achievement-template-2': CertificateCategory.ACHIEVEMENT,
  'achievement-template-3': CertificateCategory.ACHIEVEMENT,
  'achievement-template-4': CertificateCategory.ACHIEVEMENT,
  'achievement-template-5': CertificateCategory.ACHIEVEMENT,
  'achievement-template-6': CertificateCategory.ACHIEVEMENT,

  // Completion (完成证书)
  'completion-template-1': CertificateCategory.COMPLETION,
  'completion-template-2': CertificateCategory.COMPLETION,
  'completion-template-3': CertificateCategory.COMPLETION,

  // Participation (参与证书)
  'participation-template-1': CertificateCategory.PARTICIPATION,
  'participation-template-2': CertificateCategory.PARTICIPATION,
  'participation-template-3': CertificateCategory.PARTICIPATION,
  'participation-template-4': CertificateCategory.PARTICIPATION,
  'participation-template-5': CertificateCategory.PARTICIPATION,
  'participation-template-6': CertificateCategory.PARTICIPATION,

  // Excellence (优秀证书)
  'excellence-template-1': CertificateCategory.EXCELLENCE,
  'excellence-template-2': CertificateCategory.EXCELLENCE,

  // Award Certificate (奖励证书)
  'award-template-1': CertificateCategory.AWARD_CERTIFICATE,
  'award-template-2': CertificateCategory.AWARD_CERTIFICATE,

  // Recognition Certificate (表彰证书)
  'recognition-template-1': CertificateCategory.RECOGNITION_CERTIFICATE,
  'recognition-template-2': CertificateCategory.RECOGNITION_CERTIFICATE,

  // Sports Certificate (体育证书)
  'sports-template-1': CertificateCategory.SPORTS_CERTIFICATE,
  'sports-template-2': CertificateCategory.SPORTS_CERTIFICATE,

  // Graduation Certificate (毕业证书)
  'graduation-template-1': CertificateCategory.GRADUATION_CERTIFICATE,

  // Training Certificate (培训证书)
  'training-template-1': CertificateCategory.TRAINING_CERTIFICATE,

  // Funny Certificate (搞笑证书)
  'funny-template-1': CertificateCategory.FUNNY_CERTIFICATE,
  'funny-template-2': CertificateCategory.FUNNY_CERTIFICATE,

  // Employee Certificate (员工证书)
  'employee-template-1': CertificateCategory.EMPLOYEE_CERTIFICATE,

  // Volunteer Certificate (志愿者证书)
  'volunteer-template-1': CertificateCategory.VOLUNTEER_CERTIFICATE,

} as const;

/**
 * 根据分类获取模板
 */
export function getTemplatesByCategory(category: CertificateCategory): CertificateTemplate[] {
  return CERTIFICATE_TEMPLATES.filter(template => template.category === category);
}

/**
 * 根据模板名称获取模板
 */
export function getTemplateByName(name: string): CertificateTemplate | undefined {
  return CERTIFICATE_TEMPLATES.find(template => template.name === name);
}

/**
 * 根据ID获取模板
 */
export function getTemplateById(id: string): CertificateTemplate | undefined {
  return CERTIFICATE_TEMPLATES.find(template => template.id === id);
}

/**
 * 获取所有可用的模板
 */
export function getAllTemplates(): CertificateTemplate[] {
  return CERTIFICATE_TEMPLATES.filter(template => template.status === 'open');
}

/**
 * 获取即将推出的模板
 */
export function getComingSoonTemplates(): CertificateTemplate[] {
  return CERTIFICATE_TEMPLATES.filter(template => template.status === 'coming-soon');
}

/**
 * 根据标签搜索模板
 */
export function getTemplatesByTags(tags: string[]): CertificateTemplate[] {
  return CERTIFICATE_TEMPLATES.filter(template =>
    tags.some(tag => template.tags.includes(tag.toLowerCase()))
  );
}

/**
 * 获取高搜索量分类的模板
 */
export function getHighSEOValueTemplates(): CertificateTemplate[] {
  const highSEOCategories = [
    CertificateCategory.AWARD_CERTIFICATE,
    CertificateCategory.GRADUATION_CERTIFICATE,
    CertificateCategory.SPORTS_CERTIFICATE,
    CertificateCategory.RECOGNITION_CERTIFICATE,
    CertificateCategory.TRAINING_CERTIFICATE,
    CertificateCategory.FUNNY_CERTIFICATE,
    CertificateCategory.EMPLOYEE_CERTIFICATE,
    CertificateCategory.VOLUNTEER_CERTIFICATE
  ];

  return CERTIFICATE_TEMPLATES.filter(template =>
    highSEOCategories.includes(template.category)
  );
}

/**
 * 获取分类的模板数量
 */
export function getTemplateCountByCategory(category: CertificateCategory): number {
  return CERTIFICATE_TEMPLATES.filter(template => template.category === category).length;
}

/**
 * 获取所有分类的模板统计
 */
export function getCategoryTemplateStats(): Record<CertificateCategory, number> {
  const stats = {} as Record<CertificateCategory, number>;

  Object.values(CertificateCategory).forEach(category => {
    stats[category] = getTemplateCountByCategory(category);
  });

  return stats;
}
