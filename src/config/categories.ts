import { CertificateCategory } from '@/types/certificate';

/**
 * 分类配置文件
 * 用于控制哪些分类在网站上显示和可用
 */

export interface CategoryConfig {
  category: CertificateCategory;
  enabled: boolean;
  reason?: string; // 禁用原因
  order: number; // 显示顺序
}

/**
 * 分类启用配置
 * 基于SEO最佳实践和搜索量数据排序
 * 可以通过修改这个配置来控制分类的显示
 */
export const CATEGORY_CONFIGS: CategoryConfig[] = [
  // 高搜索量分类 - 优先显示
  {
    category: CertificateCategory.GIFT_CERTIFICATE,
    enabled: false,
    reason: 'Templates in development - high SEO priority',
    order: 1
  },
  {
    category: CertificateCategory.AWARD_CERTIFICATE,
    enabled: false,
    reason: 'Templates in development - high SEO priority',
    order: 2
  },
  {
    category: CertificateCategory.GRADUATION_CERTIFICATE,
    enabled: false,
    reason: 'Templates in development - seasonal high demand',
    order: 3
  },
  {
    category: CertificateCategory.SPORTS_CERTIFICATE,
    enabled: false,
    reason: 'Templates in development - high SEO priority',
    order: 4
  },

  // 原有分类 - 已有模板
  {
    category: CertificateCategory.COMPLETION,
    enabled: true,
    order: 7
  },
  {
    category: CertificateCategory.ACHIEVEMENT,
    enabled: true,
    order: 8
  },

  // 其他新分类
  {
    category: CertificateCategory.TRAINING_CERTIFICATE,
    enabled: false,
    reason: 'Templates in development - professional demand',
    order: 9
  },
  {
    category: CertificateCategory.EMPLOYEE_CERTIFICATE,
    enabled: false,
    reason: 'Templates in development - corporate demand',
    order: 10
  },
  {
    category: CertificateCategory.VOLUNTEER_CERTIFICATE,
    enabled: false,
    reason: 'Templates in development - community demand',
    order: 11
  },
  {
    category: CertificateCategory.RECOGNITION_CERTIFICATE,
    enabled: false,
    reason: 'Templates in development - general recognition',
    order: 12
  },
  {
    category: CertificateCategory.FUNNY_CERTIFICATE,
    enabled: false,
    reason: 'Templates in development - entertainment value',
    order: 13
  },
  {
    category: CertificateCategory.BIRTH_CERTIFICATE,
    enabled: false,
    reason: 'Templates in development - legal considerations needed',
    order: 14
  },

  // 原有分类 - 待开发
  {
    category: CertificateCategory.PARTICIPATION,
    enabled: false,
    reason: 'Templates are placeholder data, not ready for production',
    order: 15
  },
  {
    category: CertificateCategory.EXCELLENCE,
    enabled: false,
    reason: 'Templates are placeholder data, not ready for production',
    order: 16
  }
];

/**
 * 获取分类配置
 */
export function getCategoryConfig(category: CertificateCategory): CategoryConfig | undefined {
  return CATEGORY_CONFIGS.find(config => config.category === category);
}

/**
 * 检查分类是否启用
 */
export function isCategoryEnabled(category: CertificateCategory): boolean {
  const config = getCategoryConfig(category);
  return config?.enabled ?? false;
}

/**
 * 获取启用的分类列表（按顺序排序）
 */
export function getEnabledCategories(): CertificateCategory[] {
  return CATEGORY_CONFIGS
    .filter(config => config.enabled)
    .sort((a, b) => a.order - b.order)
    .map(config => config.category);
}

/**
 * 获取所有分类列表（按顺序排序）
 */
export function getAllCategoriesOrdered(): CertificateCategory[] {
  return CATEGORY_CONFIGS
    .sort((a, b) => a.order - b.order)
    .map(config => config.category);
}

/**
 * 开发环境配置
 * 在开发环境中可以显示所有分类（包括disabled的）用于测试
 */
export const DEV_CONFIG = {
  showDisabledCategories: process.env.NODE_ENV === 'development',
  showDisabledReason: true
};
