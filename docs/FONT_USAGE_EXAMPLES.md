# 🎨 字体使用示例

## 新增字体配置

我们已经成功添加了以下新字体到项目中：

### 1. Pinyon Script
- **类型**: 手写/装饰字体
- **用途**: 适合高端证书签名和装饰标题
- **CSS类**: `font-pinyon`
- **变量**: `var(--font-pinyon-script)`

### 2. Libre Baskerville  
- **类型**: 传统衬线字体
- **用途**: 适合正式文档和经典证书
- **CSS类**: `font-libre`
- **变量**: `var(--font-libre-baskerville)`

### 3. E<PERSON>
- **类型**: 古典衬线字体
- **用途**: 适合高端和历史主题证书
- **CSS类**: `font-eb-garamond`
- **变量**: `var(--font-eb-garamond)`

## 在 certificate-templates.ts 中使用

### 示例1: 使用 Pinyon Script 作为签名字体

```typescript
{
  id: 'elegant-certificate-1',
  name: 'elegant-certificate-1',
  displayName: 'Elegant Certificate with Pinyon Script',
  // ... 其他配置
  style: {
    fonts: {
      name: {
        family: 'Playfair Display',
        size: 32,
        weight: 600,
        color: '#1F2937',
      },
      body: {
        family: 'Inter',
        size: 14,
        weight: 400,
        color: '#374151',
      },
      signature: {
        family: 'Pinyon Script',  // 使用新字体
        size: 28,
        weight: 400,
        color: '#1F2937',
      },
    },
  },
  layout: {
    name: {
      // ... 位置配置
      fontFamily: 'Playfair Display',
      // ... 其他样式
    },
    signature: {
      // ... 位置配置
      fontFamily: 'Pinyon Script',  // 使用新字体
      fontSize: 28,
      fontWeight: 400,
      // ... 其他样式
    },
  },
}
```

### 示例2: 使用 Libre Baskerville 作为标题字体

```typescript
{
  id: 'traditional-certificate-1',
  name: 'traditional-certificate-1',
  displayName: 'Traditional Certificate with Libre Baskerville',
  // ... 其他配置
  style: {
    fonts: {
      name: {
        family: 'Libre Baskerville',  // 使用新字体
        size: 30,
        weight: 700,
        color: '#1F2937',
      },
      body: {
        family: 'Crimson Text',
        size: 14,
        weight: 400,
        color: '#374151',
      },
      signature: {
        family: 'Dancing Script',
        size: 24,
        weight: 400,
        color: '#1F2937',
      },
    },
  },
  layout: {
    name: {
      // ... 位置配置
      fontFamily: 'Libre Baskerville',  // 使用新字体
      fontSize: 30,
      fontWeight: 700,
      // ... 其他样式
    },
  },
}
```

### 示例3: 使用 EB Garamond 创建古典风格证书

```typescript
{
  id: 'classical-certificate-1',
  name: 'classical-certificate-1',
  displayName: 'Classical Certificate with EB Garamond',
  // ... 其他配置
  style: {
    fonts: {
      name: {
        family: 'EB Garamond',  // 使用新字体
        size: 34,
        weight: 600,
        color: '#1F2937',
      },
      body: {
        family: 'EB Garamond',  // 正文也使用同一字体
        size: 16,
        weight: 400,
        color: '#374151',
      },
      signature: {
        family: 'Pinyon Script',  // 签名使用手写字体
        size: 26,
        weight: 400,
        color: '#1F2937',
      },
    },
  },
  layout: {
    name: {
      // ... 位置配置
      fontFamily: 'EB Garamond',  // 使用新字体
      fontSize: 34,
      fontWeight: 600,
      // ... 其他样式
    },
    details: {
      // ... 位置配置
      fontFamily: 'EB Garamond',  // 正文使用同一字体
      fontSize: 16,
      fontWeight: 400,
      // ... 其他样式
    },
    signature: {
      // ... 位置配置
      fontFamily: 'Pinyon Script',  // 签名使用手写字体
      fontSize: 26,
      fontWeight: 400,
      // ... 其他样式
    },
  },
}
```

## 字体组合推荐

### 1. 优雅组合
- **标题**: EB Garamond (古典衬线)
- **正文**: Crimson Text (传统衬线)
- **签名**: Pinyon Script (精致手写)

### 2. 传统组合
- **标题**: Libre Baskerville (传统衬线)
- **正文**: EB Garamond (古典衬线)
- **签名**: Dancing Script (手写)

### 3. 现代古典组合
- **标题**: Playfair Display (现代衬线)
- **正文**: Inter (现代无衬线)
- **签名**: Pinyon Script (精致手写)

## 字体权重支持

### Pinyon Script
- 支持权重: 400 (Regular)

### Libre Baskerville
- 支持权重: 400 (Regular), 700 (Bold)

### EB Garamond
- 支持权重: 400 (Regular), 500 (Medium), 600 (SemiBold), 700 (Bold)

## 使用工具函数

您可以使用 `src/lib/fonts.ts` 中的工具函数来获取字体信息：

```typescript
import { getFontCssClass, getFontCssVariable, getPdfFontKey } from '@/lib/fonts';

// 获取CSS类名
const cssClass = getFontCssClass('Pinyon Script'); // 'font-pinyon'

// 获取CSS变量
const cssVariable = getFontCssVariable('Libre Baskerville'); // 'var(--font-libre-baskerville)'

// 获取PDF字体键名
const pdfKey = getPdfFontKey('EB Garamond'); // 'EB Garamond'
```

## 注意事项

1. **字体加载**: 所有字体都通过 Next.js 的 `next/font/google` 进行优化加载
2. **性能**: 字体会自动进行子集化和优化
3. **兼容性**: 每个字体都配置了合适的fallback字体
4. **权重**: 请确保使用字体支持的权重值
5. **PDF生成**: 字体在PDF生成时会自动嵌入，确保显示一致性

现在您可以在 `src/lib/certificate-templates.ts` 中直接使用这些新字体了！
